<template>
  <div class="statistics-overview-test">
    <h2>StatisticsOverview组件测试</h2>
    
    <!-- 组件展示 -->
    <div class="component-demo">
      <StatisticsOverview 
        :project-data="testProjectData"
        :party-building-data="testPartyBuildingData"
        :loading="loading"
        @card-click="handleCardClick"
        @data-refresh="handleDataRefresh"
      />
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
      <a-card title="测试控制">
        <a-space direction="vertical" style="width: 100%">
          <a-space>
            <a-button @click="loadTestData" type="primary">加载测试数据</a-button>
            <a-button @click="clearData">清空数据</a-button>
            <a-button @click="toggleLoading">切换加载状态</a-button>
            <a-button @click="randomizeData">随机化数据</a-button>
          </a-space>
          
          <a-divider />
          
          <div>
            <h4>数据调整</h4>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="项目总数">
                  <a-input-number 
                    v-model:value="testProjectData.totalProjects" 
                    :min="0" 
                    :max="1000"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="完成项目">
                  <a-input-number 
                    v-model:value="testProjectData.completedProjects" 
                    :min="0" 
                    :max="testProjectData.totalProjects"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            
            <a-row :gutter="16">
              <a-col :span="6">
                <a-form-item label="优秀单位">
                  <a-input-number 
                    v-model:value="testPartyBuildingData.excellentCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="良好单位">
                  <a-input-number 
                    v-model:value="testPartyBuildingData.goodCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="一般单位">
                  <a-input-number 
                    v-model:value="testPartyBuildingData.averageCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="6">
                <a-form-item label="待提升单位">
                  <a-input-number 
                    v-model:value="testPartyBuildingData.poorCount" 
                    :min="0" 
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-space>
      </a-card>
    </div>
    
    <!-- 事件日志 -->
    <div class="event-log">
      <a-card title="事件日志">
        <div class="log-container">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <a-tag :color="getLogColor(log.type)">{{ log.type }}</a-tag>
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <a-button @click="clearLogs" size="small" style="margin-top: 8px;">清空日志</a-button>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import StatisticsOverview from './StatisticsOverview.vue'
import type { ProjectData, PartyBuildingData } from '../types'
import { mockDataService } from '../mock/data'

// 响应式数据
const loading = ref(false)
const eventLogs = ref<Array<{ type: string; time: string; message: string }>>([])

const testProjectData = ref<ProjectData>({
  totalProjects: 156,
  completedProjects: 142,
  completionRate: 91.0,
  applicantUnits: 89,
  cultivationUnits: 67,
  benchmarkUnits: 34,
  applicantRatio: 57.1,
  cultivationRatio: 43.0,
  benchmarkRatio: 21.8,
  totalIndicators: 1247,
  smartFilterIndicators: 892,
  dataResources: 2156,
  dataSourceUnits: 78,
  regionType: 'municipal',
  regionName: '重庆市'
})

const testPartyBuildingData = ref<PartyBuildingData>({
  excellentCount: 45,
  goodCount: 78,
  averageCount: 34,
  poorCount: 12,
  averageIndex: 82.5,
  targetIndex: 85.0,
  completionRate: 97.1,
  municipalAgencies: 89,
  partyGroupDepartments: 45,
  governmentDepartments: 44,
  agencies: []
})

// 计算属性
const totalUnits = computed(() => {
  return testPartyBuildingData.value.excellentCount + 
         testPartyBuildingData.value.goodCount + 
         testPartyBuildingData.value.averageCount + 
         testPartyBuildingData.value.poorCount
})

// 方法
const loadTestData = async () => {
  loading.value = true
  addLog('INFO', '开始加载测试数据')
  
  try {
    const [projectResponse, partyBuildingResponse] = await Promise.all([
      mockDataService.fetchProjectStats(),
      mockDataService.fetchPartyBuildingData()
    ])
    
    testProjectData.value = projectResponse.data.municipal
    testPartyBuildingData.value = partyBuildingResponse.data
    
    addLog('SUCCESS', '测试数据加载成功')
  } catch (error) {
    addLog('ERROR', `数据加载失败: ${error}`)
  } finally {
    loading.value = false
  }
}

const clearData = () => {
  testProjectData.value = {
    totalProjects: 0,
    completedProjects: 0,
    completionRate: 0,
    applicantUnits: 0,
    cultivationUnits: 0,
    benchmarkUnits: 0,
    applicantRatio: 0,
    cultivationRatio: 0,
    benchmarkRatio: 0,
    totalIndicators: 0,
    smartFilterIndicators: 0,
    dataResources: 0,
    dataSourceUnits: 0,
    regionType: 'municipal',
    regionName: '重庆市'
  }
  
  testPartyBuildingData.value = {
    excellentCount: 0,
    goodCount: 0,
    averageCount: 0,
    poorCount: 0,
    averageIndex: 0,
    targetIndex: 0,
    completionRate: 0,
    municipalAgencies: 0,
    partyGroupDepartments: 0,
    governmentDepartments: 0,
    agencies: []
  }
  
  addLog('INFO', '数据已清空')
}

const toggleLoading = () => {
  loading.value = !loading.value
  addLog('INFO', `加载状态: ${loading.value ? '开启' : '关闭'}`)
}

const randomizeData = () => {
  testProjectData.value.totalProjects = Math.floor(Math.random() * 200) + 50
  testProjectData.value.completedProjects = Math.floor(Math.random() * testProjectData.value.totalProjects)
  testProjectData.value.completionRate = Math.round((testProjectData.value.completedProjects / testProjectData.value.totalProjects) * 100)
  
  testPartyBuildingData.value.excellentCount = Math.floor(Math.random() * 50)
  testPartyBuildingData.value.goodCount = Math.floor(Math.random() * 80)
  testPartyBuildingData.value.averageCount = Math.floor(Math.random() * 40)
  testPartyBuildingData.value.poorCount = Math.floor(Math.random() * 20)
  
  addLog('INFO', '数据已随机化')
}

const handleCardClick = (type: string, data: any) => {
  addLog('CLICK', `点击了卡片: ${type}`)
  console.log('Card clicked:', type, data)
}

const handleDataRefresh = () => {
  addLog('REFRESH', '触发数据刷新')
  loadTestData()
}

const addLog = (type: string, message: string) => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.unshift({ type, time, message })
  
  // 保持最多50条日志
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

const clearLogs = () => {
  eventLogs.value = []
}

const getLogColor = (type: string) => {
  const colors = {
    INFO: 'blue',
    SUCCESS: 'green',
    ERROR: 'red',
    CLICK: 'purple',
    REFRESH: 'orange'
  }
  return colors[type as keyof typeof colors] || 'default'
}

// 生命周期
onMounted(() => {
  addLog('INFO', 'StatisticsOverview测试组件已加载')
})
</script>

<style scoped>
.statistics-overview-test {
  padding: 20px;
}

.component-demo {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.control-panel {
  margin-bottom: 24px;
}

.event-log {
  .log-container {
    max-height: 300px;
    overflow-y: auto;
    
    .log-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;
      
      .log-time {
        margin: 0 8px;
        color: #666;
        min-width: 80px;
      }
      
      .log-message {
        flex: 1;
      }
    }
  }
}
</style>
