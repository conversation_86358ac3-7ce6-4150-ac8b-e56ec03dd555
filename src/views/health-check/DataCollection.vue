<template>
  <div class="data-collection-page">
    <!-- 页面头部操作区 -->
    <div class="page-header">
      <div class="header-title">
        <h2>数据收集与继承</h2>
        <p>全面接入各类数据源，确保数据的完整性、准确性、一致性和安全性</p>
      </div>
      <div class="header-actions">
        <a-space size="middle">
          <a-button type="primary" @click="showDataSourceModal">
            <template #icon><plus-outlined /></template>
            添加数据源
          </a-button>
          <a-button @click="syncAllDataSources" :loading="syncingAll">
            <template #icon><sync-outlined /></template>
            全量同步
          </a-button>
          <a-button @click="testConnections">
            <template #icon><api-outlined /></template>
            测试连接
          </a-button>
          <a-button @click="refreshData">
            <template #icon><reload-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 数据源状态概览 -->
    <div class="overview-section">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="数据源总数"
              :value="dataSourceStats.totalSources"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <database-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="在线数据源"
              :value="dataSourceStats.onlineSources"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <check-circle-outlined />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="今日同步量"
              :value="dataSourceStats.todaySyncCount"
              :value-style="{ color: '#faad14' }"
            >
              <template #prefix>
                <cloud-sync-outlined />
              </template>
              <template #suffix>条</template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :xs="24" :sm="12" :md="6">
          <a-card class="status-card">
            <a-statistic
              title="数据质量评分"
              :value="dataSourceStats.qualityScore"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <trophy-outlined />
              </template>
              <template #suffix>分</template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 数据源分类标签页 -->
    <div class="data-source-tabs-section">
      <a-card>
        <a-tabs v-model:activeKey="activeSourceType" @change="handleSourceTypeChange">
          <a-tab-pane key="all" tab="全部数据源">
            <div class="source-type-info">
              <a-alert 
                message="全部数据源" 
                description="显示系统中接入的所有数据源，包括数据库、API接口、文件系统、外部系统等。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="database" tab="数据库">
            <div class="source-type-info">
              <a-alert 
                message="数据库数据源" 
                description="MySQL、PostgreSQL、Oracle等关系型数据库以及MongoDB等非关系型数据库。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="api" tab="API接口">
            <div class="source-type-info">
              <a-alert 
                message="API接口数据源" 
                description="REST API、GraphQL、SOAP等各类API接口数据源。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="file" tab="文件系统">
            <div class="source-type-info">
              <a-alert 
                message="文件系统数据源" 
                description="Excel、CSV、XML、JSON等各类文件格式的数据源。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
          <a-tab-pane key="external" tab="外部系统">
            <div class="source-type-info">
              <a-alert 
                message="外部系统数据源" 
                description="第三方系统、云服务、消息队列等外部系统数据源。"
                type="info" 
                show-icon 
              />
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <!-- 数据源列表 -->
    <div class="data-source-list-section">
      <a-card>
        <template #title>
          <div class="table-header">
            <span>{{ getSourceTypeTitle() }}列表</span>
            <span class="record-count">共 {{ filteredDataSources.length }} 个数据源</span>
          </div>
        </template>
        <template #extra>
          <a-space>
            <a-select v-model:value="statusFilter" placeholder="状态筛选" style="width: 120px" allow-clear>
              <a-select-option value="online">在线</a-select-option>
              <a-select-option value="offline">离线</a-select-option>
              <a-select-option value="error">异常</a-select-option>
            </a-select>
            <a-button size="small" @click="batchSync" :disabled="selectedRowKeys.length === 0">
              批量同步
            </a-button>
          </a-space>
        </template>
        <a-table
          :columns="columns"
          :data-source="filteredDataSources"
          :loading="loading"
          :pagination="paginationConfig"
          :scroll="{ x: 1600 }"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'index'">
              {{ getRowIndex(record) }}
            </template>
            <template v-else-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.connectionStatus)">
                <template #icon>
                  <loading-outlined v-if="record.isRunning" />
                  <check-circle-outlined v-else-if="record.connectionStatus === 'online'" />
                  <close-circle-outlined v-else-if="record.connectionStatus === 'offline'" />
                  <exclamation-circle-outlined v-else />
                </template>
                {{ getStatusText(record.connectionStatus) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'sourceType'">
              <a-tag :color="getSourceTypeColor(record.type)">
                {{ getSourceTypeText(record.type) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'dataQuality'">
              <div class="quality-display">
                <a-progress 
                  :percent="record.dataQualityScore" 
                  :stroke-color="getQualityColor(record.dataQualityScore)"
                  size="small"
                  :show-info="false"
                />
                <span class="quality-score">{{ record.dataQualityScore }}%</span>
              </div>
            </template>
            <template v-else-if="column.key === 'dataVolume'">
              <span>{{ formatDataVolume(record.totalRecords) }}</span>
            </template>
            <template v-else-if="column.key === 'lastSyncTime'">
              <span v-if="record.lastSyncTime">{{ formatDateTime(record.lastSyncTime) }}</span>
              <span v-else style="color: #999;">未同步</span>
            </template>
            <template v-else-if="column.key === 'syncStatus'">
              <a-tag v-if="record.isRunning" color="processing">同步中</a-tag>
              <a-tag v-else :color="getSyncStatusColor(record.syncStatus)">
                {{ getSyncStatusText(record.syncStatus) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small" @click="showDetail(record)">详情</a-button>
                <a-button type="link" size="small" @click="editDataSource(record)">编辑</a-button>
                <a-button 
                  type="link" 
                  size="small" 
                  @click="syncDataSource(record)" 
                  :loading="record.isRunning"
                  :disabled="!record.isEnabled || record.connectionStatus !== 'online'"
                >
                  同步
                </a-button>
                <a-button type="link" size="small" @click="testConnection(record)">测试</a-button>
                <a-popconfirm
                  title="确定要删除这个数据源吗？"
                  @confirm="deleteDataSource(record.id)"
                >
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 数据流监控图表 -->
    <div class="data-flow-section">
      <a-card title="数据流监控" class="flow-card">
        <template #extra>
          <a-radio-group v-model:value="flowTimeRange" button-style="solid" size="small">
            <a-radio-button value="1h">近1小时</a-radio-button>
            <a-radio-button value="6h">近6小时</a-radio-button>
            <a-radio-button value="24h">近24小时</a-radio-button>
          </a-radio-group>
        </template>
        <div class="flow-chart-container">
          <div class="flow-metrics">
            <div class="flow-metric-item">
              <div class="metric-title">总流入量</div>
              <div class="metric-value">{{ formatDataVolume(dataFlowMetrics.totalInflow) }}</div>
              <div class="metric-trend" :class="{ 'trend-up': dataFlowMetrics.inflowTrend > 0 }">
                {{ dataFlowMetrics.inflowTrend > 0 ? '↑' : '↓' }} {{ Math.abs(dataFlowMetrics.inflowTrend) }}%
              </div>
            </div>
            <div class="flow-metric-item">
              <div class="metric-title">处理速度</div>
              <div class="metric-value">{{ dataFlowMetrics.processSpeed }}/s</div>
              <div class="metric-indicator" :class="getSpeedLevel(dataFlowMetrics.processSpeed)">
                {{ getSpeedText(dataFlowMetrics.processSpeed) }}
              </div>
            </div>
            <div class="flow-metric-item">
              <div class="metric-title">错误率</div>
              <div class="metric-value">{{ dataFlowMetrics.errorRate }}%</div>
              <div class="metric-indicator" :class="getErrorLevel(dataFlowMetrics.errorRate)">
                {{ getErrorText(dataFlowMetrics.errorRate) }}
              </div>
            </div>
            <div class="flow-metric-item">
              <div class="metric-title">队列积压</div>
              <div class="metric-value">{{ dataFlowMetrics.queueBacklog }}</div>
              <div class="metric-indicator" :class="getBacklogLevel(dataFlowMetrics.queueBacklog)">
                {{ getBacklogText(dataFlowMetrics.queueBacklog) }}
              </div>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 数据源表单弹窗 -->
    <a-modal 
      :title="modalTitle" 
      :visible="formModalVisible" 
      @cancel="formModalVisible = false" 
      :footer="null" 
      width="800px"
      :mask-closable="false"
    >
      <a-form 
        :model="form" 
        :rules="rules" 
        ref="formRef" 
        :label-col="{ span: 4 }" 
        :wrapper-col="{ span: 18 }" 
        @finish="onSubmit"
      >
        <a-divider orientation="left">基本信息</a-divider>
        <a-form-item label="数据源名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入数据源名称" />
        </a-form-item>
        <a-form-item label="数据源类型" name="type">
          <a-select v-model:value="form.type" placeholder="请选择数据源类型">
            <a-select-option value="database">数据库</a-select-option>
            <a-select-option value="api">API接口</a-select-option>
            <a-select-option value="file">文件系统</a-select-option>
            <a-select-option value="external">外部系统</a-select-option>
            <a-select-option value="manual">手动录入</a-select-option>
            <a-select-option value="message_queue">消息队列</a-select-option>
            <a-select-option value="ftp">FTP服务器</a-select-option>
            <a-select-option value="sftp">SFTP服务器</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="描述说明" name="description">
          <a-textarea v-model:value="form.description" :rows="2" placeholder="请输入数据源描述" />
        </a-form-item>

        <a-divider orientation="left">连接配置</a-divider>
        <template v-if="form.type === 'database'">
          <a-form-item label="数据库类型" name="config.driverType">
            <a-select v-model:value="form.config.driverType" placeholder="请选择数据库类型">
              <a-select-option value="mysql">MySQL</a-select-option>
              <a-select-option value="postgresql">PostgreSQL</a-select-option>
              <a-select-option value="oracle">Oracle</a-select-option>
              <a-select-option value="sqlserver">SQL Server</a-select-option>
              <a-select-option value="sqlite">SQLite</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="主机地址" name="config.host">
            <a-input v-model:value="form.config.host" placeholder="请输入数据库主机地址" />
          </a-form-item>
          <a-form-item label="端口" name="config.port">
            <a-input-number v-model:value="form.config.port" placeholder="端口号" style="width: 100%" />
          </a-form-item>
          <a-form-item label="数据库名" name="config.database">
            <a-input v-model:value="form.config.database" placeholder="数据库名称" />
          </a-form-item>
          <a-form-item label="用户名" name="config.username">
            <a-input v-model:value="form.config.username" placeholder="请输入用户名" />
          </a-form-item>
          <a-form-item label="密码" name="config.password">
            <a-input-password v-model:value="form.config.password" placeholder="请输入密码" />
          </a-form-item>
        </template>
        <template v-else-if="form.type === 'api'">
          <a-form-item label="API地址" name="config.url">
            <a-input v-model:value="form.config.url" placeholder="请输入API接口地址" />
          </a-form-item>
          <a-form-item label="请求方法" name="config.method">
            <a-select v-model:value="form.config.method" placeholder="请选择请求方法">
              <a-select-option value="GET">GET</a-select-option>
              <a-select-option value="POST">POST</a-select-option>
              <a-select-option value="PUT">PUT</a-select-option>
              <a-select-option value="DELETE">DELETE</a-select-option>
            </a-select>
          </a-form-item>
        </template>
        <template v-else-if="form.type === 'file'">
          <a-form-item label="文件路径" name="config.filePath">
            <a-input v-model:value="form.config.filePath" placeholder="请输入文件路径" />
          </a-form-item>
          <a-form-item label="文件类型" name="config.fileType">
            <a-select v-model:value="form.config.fileType" placeholder="请选择文件类型">
              <a-select-option value="csv">CSV文件</a-select-option>
              <a-select-option value="excel">Excel文件</a-select-option>
              <a-select-option value="json">JSON文件</a-select-option>
              <a-select-option value="xml">XML文件</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <a-divider orientation="left">同步配置</a-divider>
        <a-form-item label="同步频率(分钟)" name="syncFrequency">
          <a-input-number 
            v-model:value="form.syncFrequency" 
            placeholder="同步频率"
            :min="1"
            :max="10080"
            style="width: 100%"
          />
          <span style="color: #666; font-size: 12px;">
            建议值：30(30分钟)，60(1小时)，1440(每天)
          </span>
        </a-form-item>
        <a-form-item label="同步类型" name="syncType">
          <a-select v-model:value="form.syncType" placeholder="请选择同步类型">
            <a-select-option value="incremental">增量同步</a-select-option>
            <a-select-option value="full">全量同步</a-select-option>
            <a-select-option value="real_time">实时同步</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="启用数据源" name="isEnabled">
          <a-switch v-model:checked="form.isEnabled" />
          <span style="margin-left: 8px; color: #666;">
            {{ form.isEnabled ? '数据源已启用' : '数据源已禁用' }}
          </span>
        </a-form-item>
        <a-form-item label="启用定时同步" name="enableSchedule">
          <a-switch v-model:checked="form.enableSchedule" />
          <span style="margin-left: 8px; color: #666;">
            {{ form.enableSchedule ? '启用定时同步' : '关闭定时同步' }}
          </span>
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 4, span: 18 }" style="margin-top: 24px">
          <a-space>
            <a-button type="primary" html-type="submit" :loading="submitting">
              {{ submitButtonText }}
            </a-button>
            <a-button @click="formModalVisible = false">取消</a-button>
            <a-button @click="testFormConnection" :loading="testing">测试连接</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 数据源详情弹窗 -->
    <a-modal 
      title="数据源详情" 
      :visible="detailVisible" 
      @cancel="detailVisible = false" 
      :footer="null" 
      width="800px"
    >
      <div class="detail-content" v-if="currentDataSource">
        <a-descriptions bordered :column="2">
          <a-descriptions-item label="数据源名称" :span="2">
            {{ currentDataSource.name }}
          </a-descriptions-item>
          <a-descriptions-item label="数据源类型">
            <a-tag :color="getSourceTypeColor(currentDataSource.type)">
              {{ getSourceTypeText(currentDataSource.type) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="连接状态">
            <a-tag :color="getStatusColor(currentDataSource.connectionStatus)">
              {{ getStatusText(currentDataSource.connectionStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="数据质量">
            <a-progress 
              :percent="currentDataSource.dataQualityScore" 
              :stroke-color="getQualityColor(currentDataSource.dataQualityScore)"
              size="small"
            />
          </a-descriptions-item>
          <a-descriptions-item label="同步频率">
            {{ getSyncFrequencyText(currentDataSource.syncFrequency) }}
          </a-descriptions-item>
          <a-descriptions-item label="最后同步">
            {{ currentDataSource.lastSyncTime ? formatDateTime(currentDataSource.lastSyncTime) : '未同步' }}
          </a-descriptions-item>
          <a-descriptions-item label="同步状态">
            <a-tag :color="getSyncStatusColor(currentDataSource.syncStatus)">
              {{ getSyncStatusText(currentDataSource.syncStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="数据量" :span="2">
            {{ formatDataVolume(currentDataSource.totalRecords) }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(currentDataSource.createTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ formatDateTime(currentDataSource.updateTime) }}
          </a-descriptions-item>
          <a-descriptions-item label="描述说明" :span="2">
            {{ currentDataSource.description || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlusOutlined,
  SyncOutlined,
  ApiOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  CloudSyncOutlined,
  TrophyOutlined
} from '@ant-design/icons-vue'

// 导入数据收集API
import {
  DataCollectionAPI,
  type DataSource,
  type DataSourceType,
  type ConnectionStatus,
  type DataCollectionStatus,
  type SyncType,
  type DataCollectionStats,
  type DataFlowMonitor,
  type DataPreview,
  type DataSourceQueryParams,
  type OperationResult,
  type BatchOperationResult
} from '@/api/data-collection'

// 原有的接口定义已移至 @/api/data-collection 模块中
// 这里使用导入的类型定义

// 响应式数据
const loading = ref(false)
const syncingAll = ref(false)
const formModalVisible = ref(false)
const detailVisible = ref(false)
const submitting = ref(false)
const testing = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const activeSourceType = ref('all')
const statusFilter = ref<string | undefined>(undefined)
const flowTimeRange = ref('24h')
const selectedRowKeys = ref<string[]>([])
const currentDataSource = ref<DataSource | null>(null)

// 表单数据
const form = ref<Partial<DataSource>>({
  name: '',
  type: 'database',
  config: {
    host: '',
    port: 3306,
    database: '',
    username: '',
    password: '',
    driverType: 'mysql'
  },
  isEnabled: true,
  enableSchedule: true,
  syncType: 'incremental',
  syncFrequency: 1440, // 每天
  description: ''
})

// 表单引用
const formRef = ref()

// 统计数据
const dataSourceStats = ref<DataCollectionStats>({
  totalSources: 0,
  onlineSources: 0,
  offlineSources: 0,
  activeSources: 0,
  enabledSources: 0,
  todaySyncCount: 0,
  todaySyncVolume: 0,
  avgSyncDuration: 0,
  totalSyncCount: 0,
  totalDataVolume: 0,
  qualityScore: 0,
  qualityTrend: [],
  avgThroughput: 0,
  peakThroughput: 0,
  successRate: 0,
  errorCount: 0,
  criticalErrors: 0,
  errorTrend: [],
  sourceTypeStats: []
})

// 数据流监控指标
const dataFlowMetrics = ref({
  totalInflow: 0,
  inflowTrend: 0,
  processSpeed: 0,
  errorRate: 0,
  queueBacklog: 0
})

// 数据源列表 - 通过API获取
const dataSourceList = ref<DataSource[]>([])

// 计算属性
const filteredDataSources = computed(() => {
  let filtered = [...dataSourceList.value]
  
  if (activeSourceType.value !== 'all') {
    filtered = filtered.filter(ds => ds.type === activeSourceType.value)
  }
  
  if (statusFilter.value) {
    // 映射状态字段
    if (statusFilter.value === 'online') {
      filtered = filtered.filter(ds => ds.connectionStatus === 'online')
    } else if (statusFilter.value === 'offline') {
      filtered = filtered.filter(ds => ds.connectionStatus === 'offline')
    } else if (statusFilter.value === 'error') {
      filtered = filtered.filter(ds => ds.connectionStatus === 'error' || ds.connectionStatus === 'timeout')
    }
  }
  
  return filtered
})

const modalTitle = computed(() => {
  return formMode.value === 'create' ? '添加数据源' : '编辑数据源'
})

const submitButtonText = computed(() => {
  return formMode.value === 'create' ? '添加' : '更新'
})

// 表格列定义
const columns = [
  { title: '序号', key: 'index', width: 80, align: 'center' },
  { title: '数据源名称', dataIndex: 'name', key: 'name', width: 200 },
  { title: '类型', key: 'sourceType', width: 100, align: 'center' },
  { title: '连接状态', key: 'status', width: 120, align: 'center' },
  { title: '数据质量', key: 'dataQuality', width: 120 },
  { title: '数据量', key: 'dataVolume', width: 100, align: 'right' },
  { title: '最后同步', key: 'lastSyncTime', width: 150 },
  { title: '同步状态', key: 'syncStatus', width: 100, align: 'center' },
  { title: '操作', key: 'action', width: 280, align: 'center', fixed: 'right' }
]

// 分页配置
const paginationConfig = {
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 个数据源`
}

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入数据源名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择数据源类型', trigger: 'change' }
  ],
  syncFrequency: [
    { required: true, message: '请输入同步频率', trigger: 'blur' },
    { type: 'number', min: 1, max: 10080, message: '同步频率范围为1-10080分钟', trigger: 'blur' }
  ],
  syncType: [
    { required: true, message: '请选择同步类型', trigger: 'change' }
  ]
}

// 方法定义
function getRowIndex(record: DataSource) {
  return filteredDataSources.value.findIndex(item => item.id === record.id) + 1
}

function getStatusColor(status: string) {
  const colors = {
    online: 'success',
    offline: 'default',
    error: 'error',
    timeout: 'warning',
    connecting: 'processing'
  }
  return colors[status] || 'default'
}

function getStatusText(status: string) {
  const texts = {
    online: '在线',
    offline: '离线',
    error: '异常',
    timeout: '超时',
    connecting: '连接中'
  }
  return texts[status] || '未知'
}

function getSourceTypeColor(type: string) {
  const colors = {
    database: 'blue',
    api: 'green',
    file: 'orange',
    external: 'purple',
    manual: 'cyan',
    message_queue: 'magenta',
    ftp: 'gold',
    sftp: 'lime'
  }
  return colors[type] || 'default'
}

function getSourceTypeText(type: string) {
  const texts = {
    database: '数据库',
    api: 'API接口',
    file: '文件系统',
    external: '外部系统',
    manual: '手动录入',
    message_queue: '消息队列',
    ftp: 'FTP服务器',
    sftp: 'SFTP服务器'
  }
  return texts[type] || '未知'
}

function getSourceTypeTitle() {
  const titles = {
    all: '全部数据源',
    database: '数据库数据源',
    api: 'API接口数据源', 
    file: '文件系统数据源',
    external: '外部系统数据源'
  }
  return titles[activeSourceType.value] || '数据源'
}

function getQualityColor(quality: number) {
  if (quality >= 90) return '#52c41a'
  if (quality >= 70) return '#faad14'
  return '#ff4d4f'
}

function getSyncStatusColor(status?: string) {
  const colors = {
    completed: 'success',
    failed: 'error',
    collecting: 'processing',
    processing: 'processing',
    pending: 'default',
    stopped: 'warning'
  }
  return colors[status || 'pending'] || 'default'
}

function getSyncStatusText(status?: string) {
  const texts = {
    completed: '已完成',
    failed: '失败',
    collecting: '采集中',
    processing: '处理中',
    pending: '待同步',
    stopped: '已停止'
  }
  return texts[status || 'pending'] || '未知'
}

function getSyncFrequencyText(frequency: number) {
  if (frequency < 60) {
    return `每${frequency}分钟`
  } else if (frequency < 1440) {
    return `每${Math.floor(frequency / 60)}小时`
  } else {
    return `每${Math.floor(frequency / 1440)}天`
  }
}

function formatDateTime(dateTime: string) {
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

function formatDataVolume(volume: number) {
  if (volume >= 1000000) {
    return (volume / 1000000).toFixed(1) + 'M'
  } else if (volume >= 1000) {
    return (volume / 1000).toFixed(1) + 'K'
  }
  return volume.toString()
}

function getSpeedLevel(speed: number) {
  if (speed >= 1000) return 'speed-fast'
  if (speed >= 500) return 'speed-normal'
  return 'speed-slow'
}

function getSpeedText(speed: number) {
  if (speed >= 1000) return '高速'
  if (speed >= 500) return '正常'
  return '缓慢'
}

function getErrorLevel(rate: number) {
  if (rate <= 1) return 'error-low'
  if (rate <= 5) return 'error-medium'
  return 'error-high'
}

function getErrorText(rate: number) {
  if (rate <= 1) return '良好'
  if (rate <= 5) return '一般'
  return '较高'
}

function getBacklogLevel(backlog: number) {
  if (backlog <= 10) return 'backlog-low'
  if (backlog <= 50) return 'backlog-medium'
  return 'backlog-high'
}

function getBacklogText(backlog: number) {
  if (backlog <= 10) return '正常'
  if (backlog <= 50) return '中等'
  return '较高'
}

async function refreshData() {
  loading.value = true
  try {
    await Promise.all([
      loadDataSources(),
      loadStats(),
      loadDataFlowMetrics()
    ])
    message.success('数据刷新成功')
  } catch (error) {
    console.error('数据刷新失败:', error)
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 加载数据源列表
async function loadDataSources() {
  try {
    const params: DataSourceQueryParams = {
      page: 1,
      pageSize: 100 // 加载所有数据源
    }
    
    const result = await DataCollectionAPI.getDataSources(params)
    dataSourceList.value = result.list
  } catch (error) {
    console.error('加载数据源列表失败:', error)
    message.error('加载数据源列表失败')
  }
}

// 加载统计数据
async function loadStats() {
  try {
    const stats = await DataCollectionAPI.getStats()
    Object.assign(dataSourceStats.value, stats)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载数据流监控指标
async function loadDataFlowMetrics() {
  try {
    const monitors = await DataCollectionAPI.getDataFlowMonitor()
    if (monitors.length > 0) {
      const monitor = monitors[0] // 使用第一个监控数据
      dataFlowMetrics.value = {
        totalInflow: monitor.currentThroughput * 3600, // 转换为小时流量
        inflowTrend: 12.5, // 模拟趋势，可以根据历史数据计算
        processSpeed: monitor.currentThroughput,
        errorRate: monitor.errorRate,
        queueBacklog: monitor.queueDepth || 0
      }
    }
  } catch (error) {
    console.error('加载数据流监控指标失败:', error)
  }
}

async function syncAllDataSources() {
  syncingAll.value = true
  try {
    const result = await DataCollectionAPI.syncAll()
    message.success(`全量同步启动完成，${result.success}/${result.total} 个数据源同步成功`)
    
    // 刷新数据源状态
    await loadDataSources()
  } catch (error) {
    console.error('全量同步失败:', error)
    message.error('全量同步失败')
  } finally {
    syncingAll.value = false
  }
}

async function testConnections() {
  loading.value = true
  try {
    const result = await DataCollectionAPI.batchTestConnections()
    message.success(`连接测试完成，${result.success}/${result.total} 个数据源连接成功`)
    
    // 刷新数据源状态
    await loadDataSources()
  } catch (error) {
    console.error('批量连接测试失败:', error)
    message.error('批量连接测试失败')
  } finally {
    loading.value = false
  }
}

function handleSourceTypeChange(key: string) {
  activeSourceType.value = key
}

function showDataSourceModal() {
  formMode.value = 'create'
  form.value = {
    name: '',
    type: 'database',
    config: {
      host: '',
      port: 3306,
      database: '',
      username: '',
      password: '',
      driverType: 'mysql'
    },
    isEnabled: true,
    enableSchedule: true,
    syncType: 'incremental',
    syncFrequency: 1440, // 每天
    description: ''
  }
  formModalVisible.value = true
}

async function editDataSource(record: DataSource) {
  try {
    // 先调用 API 获取最新编辑数据，即使可能返回 404
    const editData = await DataCollectionAPI.getDataSource(record.id)
    formMode.value = 'edit'
    form.value = { ...(editData || record) } // API 成功则用最新数据，失败则用静态数据
    currentDataSource.value = editData || record
    console.log(`✏️ 编辑数据获取成功 - 数据源: ${record.name}`)
  } catch (error) {
    // API 调用失败，使用静态数据降级
    console.warn(`编辑数据 API 调用失败，使用静态数据降级:`, error)
    formMode.value = 'edit'
    form.value = { ...record }
    currentDataSource.value = record
  }
  formModalVisible.value = true
}

async function showDetail(record: DataSource) {
  try {
    // 先调用 API 获取最新详情数据，即使可能返回 404
    const detailData = await DataCollectionAPI.getDataSource(record.id)
    currentDataSource.value = detailData || record // API 成功则用最新数据，失败则用静态数据
    console.log(`🔍 详情数据获取成功 - 数据源: ${record.name}`)
  } catch (error) {
    // API 调用失败，使用静态数据降级
    console.warn(`详情数据 API 调用失败，使用静态数据降级:`, error)
    currentDataSource.value = record
  }
  detailVisible.value = true
}

async function syncDataSource(record: DataSource) {
  if (!record.isRunning) {
    record.isRunning = true
  }
  
  try {
    const result = await DataCollectionAPI.startSync(record.id, record.syncType)
    if (result.success) {
      message.success(`${record.name} 同步启动成功`)
      // 更新本地状态
      const index = dataSourceList.value.findIndex(item => item.id === record.id)
      if (index > -1) {
        dataSourceList.value[index].syncStatus = 'collecting'
        dataSourceList.value[index].lastSyncTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
    } else {
      message.error(`${record.name} 同步启动失败: ${result.message}`)
    }
  } catch (error) {
    console.error('启动同步失败:', error)
    message.error(`${record.name} 同步启动失败`)
  } finally {
    record.isRunning = false
  }
}

async function testConnection(record: DataSource) {
  try {
    const result = await DataCollectionAPI.testConnection(record.id)
    if (result.success) {
      message.success(`${record.name} 连接测试成功`)
      
      // 更新本地连接状态
      const index = dataSourceList.value.findIndex(item => item.id === record.id)
      if (index > -1) {
        dataSourceList.value[index].connectionStatus = 'online'
        dataSourceList.value[index].lastConnectionTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      }
    } else {
      message.error(`${record.name} 连接测试失败: ${result.message}`)
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    message.error(`${record.name} 连接测试失败`)
  }
}

async function deleteDataSource(id: string) {
  try {
    const result = await DataCollectionAPI.deleteDataSource(id)
    if (result.success) {
      // 从本地列表中移除
      const index = dataSourceList.value.findIndex(item => item.id === id)
      if (index > -1) {
        dataSourceList.value.splice(index, 1)
      }
      message.success('数据源删除成功')
      // 刷新统计数据
      await loadStats()
    } else {
      message.error(`数据源删除失败: ${result.message}`)
    }
  } catch (error) {
    console.error('删除数据源失败:', error)
    message.error('数据源删除失败')
  }
}

function onSelectChange(selectedKeys: string[]) {
  selectedRowKeys.value = selectedKeys
}

async function batchSync() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要同步的数据源')
    return
  }

  try {
    const result = await DataCollectionAPI.batchSyncDataSources(selectedRowKeys.value)
    message.success(`批量同步启动完成，${result.success}/${result.total} 个数据源同步成功`)
    selectedRowKeys.value = []
    
    // 刷新数据源列表
    await loadDataSources()
  } catch (error) {
    console.error('批量同步失败:', error)
    message.error('批量同步失败')
  }
}

async function onSubmit() {
  try {
    submitting.value = true
    await formRef.value.validate()
    
    if (formMode.value === 'create') {
      const result = await DataCollectionAPI.createDataSource(form.value)
      if (result.success) {
        message.success('数据源添加成功')
        formModalVisible.value = false
        // 刷新数据源列表和统计信息
        await Promise.all([loadDataSources(), loadStats()])
      } else {
        message.error(`数据源添加失败: ${result.message}`)
      }
    } else if (currentDataSource.value) {
      const result = await DataCollectionAPI.updateDataSource(currentDataSource.value.id, form.value)
      if (result.success) {
        message.success('数据源更新成功')
        formModalVisible.value = false
        // 刷新数据源列表
        await loadDataSources()
      } else {
        message.error(`数据源更新失败: ${result.message}`)
      }
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    if (error.errorFields) {
      message.error('请检查表单输入')
    } else {
      message.error('操作失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}

async function testFormConnection() {
  testing.value = true
  try {
    // 这里可以根据表单数据构建临时数据源进行测试
    // 暂时使用模拟测试
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 模拟90%成功率
    if (Math.random() > 0.1) {
      message.success('连接测试成功')
    } else {
      message.error('连接测试失败: 连接超时')
    }
  } catch (error) {
    console.error('连接测试失败:', error)
    message.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

// 生命周期
onMounted(async () => {
  // 初始化加载数据
  await refreshData()
})
</script>

<style lang="scss" scoped>
.data-collection-page {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 20px 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-title {
      h2 {
        margin: 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 4px 0 0 0;
        color: #666;
        font-size: 14px;
      }
    }
  }

  .overview-section {
    margin-bottom: 24px;

    .status-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .data-source-tabs-section {
    margin-bottom: 24px;

    .source-type-info {
      padding: 16px 0;
    }
  }

  .data-source-list-section {
    margin-bottom: 24px;

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record-count {
        color: #666;
        font-size: 14px;
      }
    }

    .quality-display {
      display: flex;
      align-items: center;
      gap: 8px;

      .quality-score {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .data-flow-section {
    .flow-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .flow-chart-container {
      padding: 16px;

      .flow-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;

        .flow-metric-item {
          text-align: center;
          padding: 20px;
          background: #fafafa;
          border-radius: 8px;

          .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }

          .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .metric-trend {
            font-size: 12px;
            color: #52c41a;

            &.trend-up {
              color: #ff4d4f;
            }
          }

          .metric-indicator {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;

            &.speed-fast {
              background: #f6ffed;
              color: #52c41a;
            }

            &.speed-normal {
              background: #fff7e6;
              color: #faad14;
            }

            &.speed-slow {
              background: #fff1f0;
              color: #ff4d4f;
            }

            &.error-low {
              background: #f6ffed;
              color: #52c41a;
            }

            &.error-medium {
              background: #fff7e6;
              color: #faad14;
            }

            &.error-high {
              background: #fff1f0;
              color: #ff4d4f;
            }

            &.backlog-low {
              background: #f6ffed;
              color: #52c41a;
            }

            &.backlog-medium {
              background: #fff7e6;
              color: #faad14;
            }

            &.backlog-high {
              background: #fff1f0;
              color: #ff4d4f;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .data-collection-page {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .flow-metrics {
      grid-template-columns: 1fr;
    }
  }
}
</style>