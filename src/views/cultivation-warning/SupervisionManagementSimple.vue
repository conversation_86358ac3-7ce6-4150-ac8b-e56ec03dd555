<template>
  <div class="supervision-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1>督办管理</h1>
        <p>培育对象督办管理和多维分析平台</p>
      </div>
      <div class="header-actions">
        <a-space>
          <a-button type="primary">
            <plus-outlined />
            新增督办对象
          </a-button>
          <a-button>
            <sync-outlined />
            同步渝快政
          </a-button>
          <a-button>
            <download-outlined />
            导出数据
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 督办统计概览 -->
    <div class="supervision-overview">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="督办对象总数"
              :value="statistics.totalObjects"
              :value-style="{ color: '#1890ff' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="督办中"
              :value="statistics.supervisingObjects"
              :value-style="{ color: '#faad14' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="已完成"
              :value="statistics.completedObjects"
              :value-style="{ color: '#52c41a' }"
            />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card>
            <a-statistic
              title="完成率"
              :value="statistics.completionRate"
              suffix="%"
              :value-style="{ color: '#722ed1' }"
            />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 功能标签页 -->
    <a-card :bordered="false">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 督办对象列表 -->
        <a-tab-pane key="list" tab="督办对象列表">
          <div class="supervision-list">
            <!-- 搜索和筛选 -->
            <div class="search-filters">
              <a-row :gutter="16">
                <a-col :span="6">
                  <a-input
                    v-model:value="searchKeyword"
                    placeholder="搜索对象名称或负责人"
                    allow-clear
                  >
                    <template #prefix>
                      <search-outlined />
                    </template>
                  </a-input>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedType"
                    placeholder="对象类型"
                    allow-clear
                  >
                    <a-select-option value="科技型企业">科技型企业</a-select-option>
                    <a-select-option value="制造业企业">制造业企业</a-select-option>
                    <a-select-option value="服务业企业">服务业企业</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="4">
                  <a-select
                    v-model:value="selectedStatus"
                    placeholder="督办状态"
                    allow-clear
                  >
                    <a-select-option value="pending">待督办</a-select-option>
                    <a-select-option value="supervising">督办中</a-select-option>
                    <a-select-option value="completed">已完成</a-select-option>
                    <a-select-option value="overdue">已逾期</a-select-option>
                  </a-select>
                </a-col>
                <a-col :span="6">
                  <a-space>
                    <a-button @click="resetFilters">重置</a-button>
                    <a-button type="primary" @click="loadSupervisionObjects">搜索</a-button>
                  </a-space>
                </a-col>
              </a-row>
            </div>

            <!-- 督办对象表格 -->
            <div class="supervision-table">
              <a-table
                :columns="supervisionColumns"
                :data-source="supervisionList"
                :loading="loading"
                :pagination="pagination"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <div class="object-name">
                      <a @click="viewObjectDetail(record)">{{ record.name }}</a>
                      <!-- <div class="object-type">{{ record.type }}</div> -->
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'evaluationScore'">
                    <div class="score-display">
                      <a-progress 
                        type="circle" 
                        :percent="record.evaluationScore" 
                        :width="50"
                        :stroke-color="getScoreColor(record.evaluationScore)"
                      />
                      <div class="score-level">{{ record.evaluationLevel }}</div>
                    </div>
                  </template>
                  
                  <template v-if="column.key === 'supervisionStatus'">
                    <a-badge 
                      :status="getStatusBadge(record.supervisionStatus)" 
                      :text="getStatusText(record.supervisionStatus)"
                    />
                  </template>
                  
                  <template v-if="column.key === 'actions'">
                    <a-space>
                      <a-button type="link" size="small" @click="viewObjectDetail(record)">
                        详情
                      </a-button>
                      <a-button type="link" size="small" @click="editObject(record)">
                        编辑
                      </a-button>
                      <a-button type="link" size="small" @click="viewProgress(record)">
                        进度
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-tab-pane>

        <!-- 多维分析 -->
        <a-tab-pane key="analysis" tab="多维分析">
          <div class="multi-analysis">
            <a-row :gutter="24">
              <!-- 评分分布 -->
              <a-col :span="12">
                <a-card title="评分分布" size="small">
                  <div class="score-distribution">
                    <div class="distribution-chart">
                      <div v-for="level in evaluationDistribution" :key="level.name" class="level-item">
                        <div class="level-header">
                          <span class="level-name">{{ level.name }}</span>
                          <span class="level-count">{{ level.count }}个</span>
                        </div>
                        <div class="level-bar">
                          <div 
                            class="bar-fill" 
                            :style="{ 
                              width: `${(level.count / statistics.totalObjects) * 100}%`,
                              backgroundColor: level.color 
                            }"
                          ></div>
                        </div>
                        <div class="level-percent">
                          {{ Math.round((level.count / statistics.totalObjects) * 100) }}%
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
              
              <!-- 部门排名 -->
              <a-col :span="12">
                <a-card title="部门排名" size="small">
                  <div class="department-ranking">
                    <div class="ranking-list">
                      <div 
                        v-for="(dept, index) in topDepartments" 
                        :key="dept.departmentId"
                        class="ranking-item"
                      >
                        <div class="ranking-number">{{ index + 1 }}</div>
                        <div class="ranking-content">
                          <div class="dept-name">{{ dept.departmentName }}</div>
                          <div class="dept-stats">
                            <span>对象数：{{ dept.objectCount }}</span>
                            <span>完成率：{{ dept.completionRate }}%</span>
                          </div>
                        </div>
                        <div class="ranking-score">
                          <a-progress 
                            type="circle" 
                            :percent="dept.completionRate" 
                            :width="40"
                            :stroke-color="getRankingColor(index)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>

        <!-- 渝快政同步 -->
        <a-tab-pane key="sync" tab="渝快政同步">
          <div class="ykz-sync">
            <a-row :gutter="24">
              <a-col :span="12">
                <a-card title="同步状态" size="small">
                  <a-descriptions :column="1" bordered size="small">
                    <a-descriptions-item label="同步状态">
                      <a-badge :status="syncStatus.status === 'success' ? 'success' : 'error'" />
                      <span>{{ syncStatus.status === 'success' ? '正常' : '异常' }}</span>
                    </a-descriptions-item>
                    <a-descriptions-item label="最后同步">
                      {{ syncStatus.lastSync }}
                    </a-descriptions-item>
                    <a-descriptions-item label="同步成功率">
                      {{ syncStatus.successRate }}%
                    </a-descriptions-item>
                    <a-descriptions-item label="待同步对象">
                      {{ syncStatus.pendingSync }} 个
                    </a-descriptions-item>
                  </a-descriptions>
                  
                  <div class="sync-actions" style="margin-top: 16px;">
                    <a-space>
                      <a-button type="primary" @click="startFullSync" :loading="syncing">
                        全量同步
                      </a-button>
                      <a-button @click="startIncrementalSync">
                        增量同步
                      </a-button>
                      <a-button @click="viewSyncLogs">
                        同步日志
                      </a-button>
                    </a-space>
                  </div>
                </a-card>
              </a-col>
              
              <a-col :span="12">
                <a-card title="同步配置" size="small">
                  <a-form layout="vertical">
                    <a-form-item label="同步模式">
                      <a-radio-group v-model:value="syncConfig.mode">
                        <a-radio value="auto">自动同步</a-radio>
                        <a-radio value="manual">手动同步</a-radio>
                      </a-radio-group>
                    </a-form-item>
                    
                    <a-form-item label="同步频率" v-if="syncConfig.mode === 'auto'">
                      <a-select v-model:value="syncConfig.frequency" placeholder="选择同步频率">
                        <a-select-option value="realtime">实时同步</a-select-option>
                        <a-select-option value="hourly">每小时</a-select-option>
                        <a-select-option value="daily">每日</a-select-option>
                      </a-select>
                    </a-form-item>
                    
                    <a-form-item>
                      <a-space>
                        <a-button type="primary" @click="saveSyncConfig">保存配置</a-button>
                        <a-button @click="resetSyncConfig">重置</a-button>
                      </a-space>
                    </a-form-item>
                  </a-form>
                </a-card>
              </a-col>
            </a-row>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  SyncOutlined,
  DownloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const activeTab = ref('list')

// 统计数据
const statistics = reactive({
  totalObjects: 89,
  supervisingObjects: 23,
  completedObjects: 56,
  completionRate: 63
})

// 搜索和筛选
const searchKeyword = ref('')
const selectedType = ref()
const selectedStatus = ref()

// 督办对象列表
const supervisionList = ref([
  {
    id: 'obj_001',
    name: '市委办公厅',
    type: '科技型企业',
    responsiblePerson: '王东',
    responsibleDepartment: '市委直属机关工委',
    evaluationScore: 75,
    evaluationLevel: 'B级',
    supervisionStatus: 'supervising',
    expectedCompletionTime: '2024-02-15'
  },
  {
    id: 'obj_002',
    name: '市委组织部',
    type: '制造业企业',
    responsiblePerson: '黄鑫',
    responsibleDepartment: '市委直属机关工委',
    evaluationScore: 85,
    evaluationLevel: 'A级',
    supervisionStatus: 'completed',
    expectedCompletionTime: '2025-06-30'
  }
])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 督办对象表格列配置
const supervisionColumns = [
  {
    title: '对象名称',
    key: 'name',
    width: 200
  },
  {
    title: '负责人',
    dataIndex: 'responsiblePerson',
    key: 'responsiblePerson',
    width: 120
  },
  {
    title: '负责部门',
    dataIndex: 'responsibleDepartment',
    key: 'responsibleDepartment',
    width: 120
  },
  {
    title: '评分',
    key: 'evaluationScore',
    width: 120
  },
  {
    title: '督办状态',
    key: 'supervisionStatus',
    width: 120
  },
  {
    title: '预期完成',
    dataIndex: 'expectedCompletionTime',
    key: 'expectedCompletionTime',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 200
  }
]

// 评分分布数据
const evaluationDistribution = computed(() => [
  { name: 'A级', count: 25, color: '#52c41a' },
  { name: 'B级', count: 35, color: '#1890ff' },
  { name: 'C级', count: 20, color: '#faad14' },
  { name: 'D级', count: 9, color: '#ff4d4f' }
])

// 部门排名数据
const topDepartments = ref([
  {
    departmentId: 'dept_001',
    departmentName: '市科技局',
    objectCount: 30,
    completionRate: 85
  },
  {
    departmentId: 'dept_002',
    departmentName: '市经信委',
    objectCount: 25,
    completionRate: 72
  },
  {
    departmentId: 'dept_003',
    departmentName: '市发改委',
    objectCount: 20,
    completionRate: 68
  }
])

// 渝快政同步
const syncing = ref(false)
const syncStatus = reactive({
  status: 'success',
  lastSync: '2025-06-20 10:30:00',
  successRate: 95.5,
  pendingSync: 5
})

const syncConfig = reactive({
  mode: 'auto',
  frequency: 'daily'
})

// 方法
const loadSupervisionObjects = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    pagination.total = supervisionList.value.length
  }, 1000)
}

const resetFilters = () => {
  searchKeyword.value = ''
  selectedType.value = undefined
  selectedStatus.value = undefined
  loadSupervisionObjects()
}

const viewObjectDetail = (object: any) => {
  message.info(`查看督办对象详情：${object.name}`)
}

const editObject = (object: any) => {
  message.info(`编辑督办对象：${object.name}`)
}

const viewProgress = (object: any) => {
  message.info(`查看督办进度：${object.name}`)
}

const startFullSync = () => {
  syncing.value = true
  setTimeout(() => {
    syncing.value = false
    message.success('全量同步完成')
  }, 3000)
}

const startIncrementalSync = () => {
  message.success('增量同步完成')
}

const viewSyncLogs = () => {
  message.info('查看同步日志')
}

const saveSyncConfig = () => {
  message.success('同步配置保存成功')
}

const resetSyncConfig = () => {
  Object.assign(syncConfig, {
    mode: 'auto',
    frequency: 'daily'
  })
  message.info('同步配置已重置')
}

// 工具方法
const getScoreColor = (score: number) => {
  if (score >= 90) return '#52c41a'
  if (score >= 80) return '#1890ff'
  if (score >= 70) return '#faad14'
  return '#ff4d4f'
}

const getStatusBadge = (status: string) => {
  const badges = {
    pending: 'warning',
    supervising: 'processing',
    completed: 'success',
    overdue: 'error'
  }
  return badges[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: '待督办',
    supervising: '督办中',
    completed: '已完成',
    overdue: '已逾期'
  }
  return texts[status] || status
}

const getRankingColor = (index: number) => {
  const colors = ['#f5222d', '#fa541c', '#faad14', '#52c41a', '#1890ff']
  return colors[index] || '#d9d9d9'
}

// 组件挂载时加载数据
onMounted(() => {
  loadSupervisionObjects()
})
</script>

<style lang="scss" scoped>
.supervision-management {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 14px;
      }
    }
  }

  .supervision-overview {
    margin-bottom: 24px;

    .ant-card {
      text-align: center;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .search-filters {
    margin-bottom: 16px;
    padding: 16px;
    background: white;
    border-radius: 6px;
  }

  .supervision-list {
    .object-name {
      .object-type {
        font-size: 12px;
        color: #8c8c8c;
        margin-top: 4px;
      }
    }

    .score-display {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .score-level {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  .multi-analysis {
    .score-distribution {
      .distribution-chart {
        .level-item {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          gap: 12px;

          .level-header {
            width: 80px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .level-name {
              font-weight: 600;
              color: #262626;
            }

            .level-count {
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .level-bar {
            flex: 1;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            position: relative;

            .bar-fill {
              height: 100%;
              border-radius: 10px;
              transition: width 0.3s ease;
            }
          }

          .level-percent {
            width: 50px;
            text-align: right;
            font-size: 12px;
            color: #8c8c8c;
          }
        }
      }
    }

    .department-ranking {
      .ranking-list {
        .ranking-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          gap: 16px;

          &:last-child {
            border-bottom: none;
          }

          .ranking-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #8c8c8c;
          }

          .ranking-content {
            flex: 1;

            .dept-name {
              font-weight: 600;
              color: #262626;
              margin-bottom: 4px;
            }

            .dept-stats {
              display: flex;
              gap: 16px;
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .ranking-score {
            width: 200px;
          }
        }
      }
    }
  }

  .ykz-sync {
    .sync-actions {
      text-align: center;
    }
  }
}
</style>
