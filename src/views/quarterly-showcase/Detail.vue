<template>
  <div class="quarterly-showcase-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-button @click="goBack" type="text">
        <template #icon><arrow-left-outlined /></template>
        返回榜单
      </a-button>
      <div class="header-actions">
        <a-space>
          <a-button @click="exportDetail">
            <template #icon><export-outlined /></template>
            导出详情
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 基本信息卡片 -->
    <div class="basic-info-section">
      <a-card>
        <div class="basic-info">
          <div class="avatar-section">
            <a-avatar :size="80" :src="detailData?.target.avatar">
              {{ detailData?.target.name.charAt(0) }}
            </a-avatar>
            <div class="rank-badge">
              <a-badge 
                :count="detailData?.rank" 
                :number-style="getRankStyle(detailData?.rank || 0)"
              />
            </div>
          </div>
          <div class="info-section">
            <h2>{{ detailData?.target.name }}</h2>
            <!-- <p class="department">{{ detailData?.target.department }} · {{ detailData?.target.position }}</p> -->
              <p class="department">{{ detailData?.target.department }} </p>
            <div class="score-overview">
              <a-row :gutter="24">
                <a-col :span="8">
                  <a-statistic title="当前分数" :value="detailData?.score" :precision="1" />
                </a-col>
                <a-col :span="8">
                  <a-statistic title="季度得分" :value="detailData?.quarterlyScore" :precision="1" />
                </a-col>
                <a-col :span="8">
                  <a-statistic title="年度得分" :value="detailData?.yearlyScore" :precision="1" />
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 项目信息 -->
    <div class="project-info-section">
      <a-card title="项目信息">
        <div class="project-details">
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="项目名称">
              {{ detailData?.project.name }}
            </a-descriptions-item>
            <a-descriptions-item label="项目类别">
              <a-tag color="blue">{{ detailData?.project.category }}</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="项目权重">
              {{ detailData?.project.weight }}%
            </a-descriptions-item>
            <a-descriptions-item label="排名趋势">
              <a-tag 
                :color="getTrendColor(detailData?.trend || 'stable')"
              >
                <template #icon>
                  <arrow-up-outlined v-if="detailData?.trend === 'up'" />
                  <arrow-down-outlined v-if="detailData?.trend === 'down'" />
                  <minus-outlined v-if="detailData?.trend === 'stable'" />
                </template>
                {{ getTrendText(detailData?.trend || 'stable') }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="项目描述" :span="2">
              {{ detailData?.project.description || '暂无描述' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-card>
    </div>

    <!-- 详细分数 -->
    <div class="score-details-section">
      <a-card title="详细分数">
        <a-table
          :columns="scoreColumns"
          :data-source="detailData?.details || []"
          :pagination="false"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <!-- 评分项目列 -->
            <template v-if="column.key === 'itemName'">
              <div class="item-name">
                <strong>{{ record.itemName }}</strong>
                <p v-if="record.description" class="item-description">
                  {{ record.description }}
                </p>
              </div>
            </template>

            <!-- 得分列 -->
            <template v-else-if="column.key === 'score'">
              <div class="score-cell">
                <div class="score-bar">
                  <a-progress
                    :percent="(record.score / record.fullScore) * 100"
                    :show-info="false"
                    :stroke-color="getScoreColor(record.score / record.fullScore)"
                  />
                </div>
                <div class="score-text">
                  {{ record.score }} / {{ record.fullScore }}
                </div>
              </div>
            </template>

            <!-- 权重列 -->
            <template v-else-if="column.key === 'weight'">
              <a-tag color="orange">{{ record.weight }}%</a-tag>
            </template>

            <!-- 证据材料列 -->
            <template v-else-if="column.key === 'evidences'">
              <div class="evidences-cell">
                <a-space v-if="record.evidences && record.evidences.length > 0">
                  <a-tooltip
                    v-for="evidence in record.evidences"
                    :key="evidence.id"
                    :title="evidence.name"
                  >
                    <a-tag
                      :color="getEvidenceColor(evidence.type)"
                      style="cursor: pointer"
                      @click="viewEvidence(evidence)"
                    >
                      {{ getEvidenceIcon(evidence.type) }} {{ evidence.type }}
                    </a-tag>
                  </a-tooltip>
                </a-space>
                <span v-else class="no-evidence">暂无证据</span>
              </div>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>

    <!-- 历史趋势 -->
    <div class="trend-section">
      <a-card title="历史趋势分析">
        <div class="trend-chart">
          <!-- 简单的趋势展示 -->
          <div class="trend-visualization">
            <h4>季度排名变化</h4>
            <div class="trend-line">
              <div class="trend-points">
                <div
                  v-for="(point, index) in trendPoints"
                  :key="index"
                  class="trend-point"
                  :style="{
                    left: `${(index / (trendPoints.length - 1)) * 100}%`,
                    bottom: `${((20 - point.rank) / 20) * 100}%`
                  }"
                >
                  <div class="point-dot"></div>
                  <div class="point-label">
                    <div class="quarter">{{ point.quarter }}</div>
                    <div class="rank">第{{ point.rank }}名</div>
                    <div class="score">{{ point.score }}分</div>
                  </div>
                </div>
              </div>
              <!-- 连接线 -->
              <svg class="trend-svg" viewBox="0 0 100 100" preserveAspectRatio="none">
                <polyline
                  :points="trendLinePoints"
                  fill="none"
                  stroke="#1890ff"
                  stroke-width="2"
                />
              </svg>
            </div>
          </div>

          <div class="trend-summary">
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic
                  title="上季度排名"
                  :value="detailData?.lastQuarterRank || 0"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="排名变化"
                  :value="detailData?.trendValue || 0"
                  :value-style="{
                    color: (detailData?.trendValue || 0) >= 0 ? '#3f8600' : '#cf1322'
                  }"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="连续上榜"
                  :value="4"
                  suffix="季度"
                />
              </a-col>
              <a-col :span="6">
                <a-statistic
                  title="最佳排名"
                  :value="Math.min(...trendPoints.map(p => p.rank))"
                />
              </a-col>
            </a-row>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ArrowLeftOutlined,
  ExportOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined
} from '@ant-design/icons-vue'
import type { RankingItem, Evidence } from '@/types/quarterly-showcase'
import { exportDetailToExcel } from '@/utils/export'

const router = useRouter()
const route = useRoute()

// 响应式数据
const detailData = ref<RankingItem | null>(null)

// 趋势数据
const trendPoints = ref([
  { quarter: '2024Q1', rank: 5, score: 85.2 },
  { quarter: '2024Q2', rank: 3, score: 89.1 },
  { quarter: '2024Q3', rank: 3, score: 91.8 },
  { quarter: '2024Q4', rank: 1, score: 95.5 }
])

// 计算趋势线坐标点
const trendLinePoints = computed(() => {
  return trendPoints.value
    .map((point, index) => {
      const x = (index / (trendPoints.value.length - 1)) * 100
      const y = 100 - ((20 - point.rank) / 20) * 100
      return `${x},${y}`
    })
    .join(' ')
})

// 分数详情表格列配置
const scoreColumns = [
  {
    title: '评分项目',
    dataIndex: 'itemName',
    key: 'itemName',
    width: 200
  },
  {
    title: '得分',
    dataIndex: 'score',
    key: 'score',
    width: 150
  },
  {
    title: '权重',
    dataIndex: 'weight',
    key: 'weight',
    width: 80
  },
  {
    title: '证据材料',
    dataIndex: 'evidences',
    key: 'evidences'
  }
]

// 方法
const goBack = () => {
  router.back()
}

const exportDetail = () => {
  try {
    if (!detailData.value) {
      message.warning('暂无数据可导出')
      return
    }

    exportDetailToExcel(detailData.value)
    message.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

const getRankStyle = (rank: number) => {
  if (rank <= 3) {
    return { backgroundColor: '#faad14', color: '#fff' }
  }
  return { backgroundColor: '#d9d9d9', color: '#666' }
}

const getTrendColor = (trend: string) => {
  switch (trend) {
    case 'up': return 'green'
    case 'down': return 'red'
    default: return 'default'
  }
}

const getTrendText = (trend: string) => {
  switch (trend) {
    case 'up': return '上升'
    case 'down': return '下降'
    default: return '稳定'
  }
}

const getScoreColor = (ratio: number) => {
  if (ratio >= 0.9) return '#52c41a'
  if (ratio >= 0.7) return '#faad14'
  return '#ff4d4f'
}

const getEvidenceColor = (type: string) => {
  switch (type) {
    case 'document': return 'blue'
    case 'image': return 'green'
    case 'video': return 'purple'
    case 'link': return 'orange'
    default: return 'default'
  }
}

const getEvidenceIcon = (type: string) => {
  switch (type) {
    case 'document': return '📄'
    case 'image': return '🖼️'
    case 'video': return '🎥'
    case 'link': return '🔗'
    default: return '📎'
  }
}

const viewEvidence = (evidence: Evidence) => {
  console.log('查看证据:', evidence)
  // 这里可以实现查看证据的逻辑
}

// 初始化Mock数据
const initMockData = () => {
  const id = route.params.id as string

  // 根据ID生成不同的Mock数据
  const mockDataMap: Record<string, any> = {
    '1': {
      id: '1',
      rank: 1,
      target: {
        id: '1',
        name: '市委统战部',
        department: '办公室',
        position: '主任',
        avatar: ''
      },
      project: {
        id: '1',
        name: '党建工作示范项目',
        category: '党建工作',
        description: '深入推进党建工作标准化规范化，打造党建工作示范点，提升党建工作质量和水平。',
        weight: 25
      },
      score: 95.5,
      quarterlyScore: 92.3,
      yearlyScore: 368.5,
      trend: 'up',
      trendValue: 2,
      lastQuarterRank: 3,
      details: [
        {
          id: '1',
          itemName: '党建制度建设',
          score: 18,
          fullScore: 20,
          weight: 20,
          description: '建立健全党建工作制度体系，完善党建工作规章制度',
          evidences: [
            {
              id: '1',
              type: 'document',
              name: '党建制度汇编.pdf',
              url: '#',
              uploadTime: '2024-12-01'
            },
            {
              id: '2',
              type: 'document',
              name: '制度执行情况报告.docx',
              url: '#',
              uploadTime: '2024-11-28'
            }
          ]
        },
        {
          id: '2',
          itemName: '党员教育培训',
          score: 19,
          fullScore: 20,
          weight: 25,
          description: '组织开展党员教育培训活动，提升党员素质',
          evidences: [
            {
              id: '3',
              type: 'image',
              name: '培训现场照片集',
              url: '#',
              uploadTime: '2024-11-15'
            },
            {
              id: '4',
              type: 'video',
              name: '专题培训视频',
              url: '#',
              uploadTime: '2024-11-15'
            },
            {
              id: '5',
              type: 'document',
              name: '培训计划及总结.pdf',
              url: '#',
              uploadTime: '2024-11-20'
            }
          ]
        },
        {
          id: '3',
          itemName: '党建活动开展',
          score: 17,
          fullScore: 20,
          weight: 30,
          description: '组织开展各类党建主题活动，丰富党建工作形式',
          evidences: [
            {
              id: '6',
              type: 'image',
              name: '主题党日活动照片',
              url: '#',
              uploadTime: '2024-10-15'
            }
          ]
        },
        {
          id: '4',
          itemName: '党建工作创新',
          score: 16,
          fullScore: 20,
          weight: 25,
          description: '在党建工作方式方法上的创新实践，探索新模式',
          evidences: [
            {
              id: '7',
              type: 'link',
              name: '创新案例展示',
              url: '#',
              uploadTime: '2024-10-20'
            },
            {
              id: '8',
              type: 'document',
              name: '创新实践报告.pdf',
              url: '#',
              uploadTime: '2024-10-25'
            }
          ]
        },
        {
          id: '5',
          itemName: '党建品牌建设',
          score: 15,
          fullScore: 20,
          weight: 20,
          description: '打造具有特色的党建工作品牌',
          evidences: [
            {
              id: '9',
              type: 'image',
              name: '品牌展示材料',
              url: '#',
              uploadTime: '2024-09-30'
            }
          ]
        }
      ]
    },
    '2': {
      id: '2',
      rank: 2,
      target: {
        id: '2',
        name: '市委宣传部',
        department: '人事处',
        position: '副处长',
        avatar: ''
      },
      project: {
        id: '2',
        name: '业务能力提升项目',
        category: '业务能力',
        description: '提升业务处理能力和服务水平，优化工作流程，提高工作效率。',
        weight: 30
      },
      score: 93.2,
      quarterlyScore: 89.7,
      yearlyScore: 356.8,
      trend: 'stable',
      trendValue: 0,
      lastQuarterRank: 2,
      details: [
        {
          id: '1',
          itemName: '业务流程优化',
          score: 19,
          fullScore: 20,
          weight: 30,
          description: '优化现有业务流程，提高处理效率',
          evidences: [
            {
              id: '1',
              type: 'document',
              name: '流程优化方案.pdf',
              url: '#',
              uploadTime: '2024-11-30'
            }
          ]
        },
        {
          id: '2',
          itemName: '服务质量提升',
          score: 18,
          fullScore: 20,
          weight: 25,
          description: '提升对内对外服务质量',
          evidences: [
            {
              id: '2',
              type: 'document',
              name: '服务满意度调查报告.xlsx',
              url: '#',
              uploadTime: '2024-11-25'
            }
          ]
        },
        {
          id: '3',
          itemName: '专业技能培训',
          score: 17,
          fullScore: 20,
          weight: 25,
          description: '参加专业技能培训，提升业务能力',
          evidences: [
            {
              id: '3',
              type: 'image',
              name: '培训证书',
              url: '#',
              uploadTime: '2024-10-15'
            }
          ]
        },
        {
          id: '4',
          itemName: '工作创新实践',
          score: 16,
          fullScore: 20,
          weight: 20,
          description: '在工作中的创新实践和改进',
          evidences: []
        }
      ]
    }
  }

  // 获取对应的Mock数据，如果没有则使用默认数据
  detailData.value = mockDataMap[id] || mockDataMap['1']
}

onMounted(() => {
  initMockData()
})
</script>

<style scoped lang="scss">
.quarterly-showcase-detail {
  padding: 24px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  
  .basic-info-section {
    margin-bottom: 24px;
    
    .basic-info {
      display: flex;
      gap: 24px;
      
      .avatar-section {
        position: relative;
        
        .rank-badge {
          position: absolute;
          top: -8px;
          right: -8px;
        }
      }
      
      .info-section {
        flex: 1;
        
        h2 {
          margin: 0 0 8px 0;
          color: #262626;
        }
        
        .department {
          margin: 0 0 16px 0;
          color: #8c8c8c;
        }
        
        .score-overview {
          margin-top: 16px;
        }
      }
    }
  }
  
  .project-info-section,
  .score-details-section,
  .trend-section {
    margin-bottom: 24px;
  }
  
  .score-details-section {
    .item-name {
      .item-description {
        margin: 4px 0 0 0;
        font-size: 12px;
        color: #8c8c8c;
      }
    }
    
    .score-cell {
      .score-bar {
        margin-bottom: 4px;
      }
      
      .score-text {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
    
    .evidences-cell {
      .no-evidence {
        color: #bfbfbf;
        font-style: italic;
      }
    }
  }
  
  .trend-section {
    .trend-chart {
      .trend-visualization {
        margin-bottom: 24px;

        h4 {
          margin-bottom: 16px;
          color: #262626;
        }

        .trend-line {
          position: relative;
          height: 200px;
          background: linear-gradient(to bottom, #f0f9ff 0%, #ffffff 100%);
          border: 1px solid #e6f7ff;
          border-radius: 8px;
          padding: 20px;
          margin-bottom: 16px;

          .trend-points {
            position: relative;
            height: 100%;

            .trend-point {
              position: absolute;
              transform: translateX(-50%);

              .point-dot {
                width: 8px;
                height: 8px;
                background: #1890ff;
                border-radius: 50%;
                border: 2px solid #ffffff;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                margin: 0 auto;
              }

              .point-label {
                position: absolute;
                top: 16px;
                left: 50%;
                transform: translateX(-50%);
                text-align: center;
                font-size: 12px;
                white-space: nowrap;

                .quarter {
                  font-weight: 500;
                  color: #262626;
                }

                .rank {
                  color: #1890ff;
                  font-weight: 600;
                }

                .score {
                  color: #8c8c8c;
                }
              }
            }
          }

          .trend-svg {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            width: calc(100% - 40px);
            height: calc(100% - 40px);
            pointer-events: none;
          }
        }
      }

      .trend-summary {
        padding: 16px;
        background: #fafafa;
        border-radius: 6px;
      }
    }
  }
}

@media (max-width: 768px) {
  .quarterly-showcase-detail {
    padding: 16px;

    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .basic-info-section {
      .basic-info {
        flex-direction: column;
        text-align: center;
        gap: 16px;

        .info-section {
          h2 {
            font-size: 20px;
          }

          .score-overview {
            .ant-col {
              margin-bottom: 16px;
            }
          }
        }
      }
    }

    .project-info-section {
      .ant-descriptions {
        .ant-descriptions-item {
          padding-bottom: 12px;
        }
      }
    }

    .score-details-section {
      .ant-table-wrapper {
        overflow-x: auto;
      }

      .item-name {
        min-width: 120px;
      }

      .score-cell {
        min-width: 100px;
      }

      .evidences-cell {
        min-width: 150px;
      }
    }

    .trend-section {
      .trend-visualization {
        .trend-line {
          height: 150px;

          .trend-points {
            .trend-point {
              .point-label {
                font-size: 10px;

                .quarter,
                .rank,
                .score {
                  line-height: 1.2;
                }
              }
            }
          }
        }
      }

      .trend-summary {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .quarterly-showcase-detail {
    padding: 12px;

    .basic-info-section {
      .basic-info {
        .avatar-section {
          .ant-avatar {
            width: 60px;
            height: 60px;
            font-size: 24px;
          }
        }

        .info-section {
          h2 {
            font-size: 18px;
          }

          .department {
            font-size: 13px;
          }
        }
      }
    }

    .project-info-section,
    .score-details-section,
    .trend-section {
      .ant-card {
        .ant-card-head {
          .ant-card-head-title {
            font-size: 16px;
          }
        }

        .ant-card-body {
          padding: 12px;
        }
      }
    }

    .score-details-section {
      .ant-table {
        font-size: 12px;

        .ant-table-thead > tr > th {
          padding: 6px 4px;
          font-size: 11px;
        }

        .ant-table-tbody > tr > td {
          padding: 6px 4px;
        }
      }
    }

    .trend-section {
      .trend-visualization {
        .trend-line {
          height: 120px;
          padding: 15px;

          .trend-points {
            .trend-point {
              .point-dot {
                width: 6px;
                height: 6px;
              }

              .point-label {
                font-size: 9px;
                top: 12px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
