// 指标规则管理API - 统一Mock API对接与静态数据管理

import { http } from '@/utils/request'

// API基础路径
const API_BASE = '/api/indicator-rules'

// ==================== 静态数据定义区域 ====================

// 指标列表静态数据 - Mock API失败时的兜底数据
const STATIC_INDICATORS = [
  {
    id: '1',
    name: '党建工作完成率',
    description: '各单位党建工作任务完成情况统计',
    category: '党建工作类',
    dataSourceType: 1,
    conversionType: 1,
    conversionRules: { type: 1, rules: { onTimeScore: 100, overdueScore: 80, incompleteScore: 0 } },
    status: 1,
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-01 10:00:00'
  },
  {
    id: '2',
    name: '党员满意度调查',
    description: '党员对组织生活满意度调查结果',
    category: '组织建设类',
    dataSourceType: 2,
    surveyTitle: '党建工作满意度调查',
    conversionType: 5,
    conversionRules: { type: 5, rules: {} },
    status: 1,
    createTime: '2025-01-02 10:00:00',
    updateTime: '2025-01-02 10:00:00'
  },
  {
    id: '3',
    name: '优秀党员评选',
    description: '年度优秀党员评选投票结果',
    category: '组织建设类',
    dataSourceType: 3,
    voteTitle: '优秀党员评选',
    conversionType: 6,
    conversionRules: { type: 6, rules: {} },
    status: 1,
    createTime: '2025-01-03 10:00:00',
    updateTime: '2025-01-03 10:00:00'
  },
  {
    id: '4',
    name: '制度执行情况',
    description: '各项制度的执行和落实情况评估',
    category: '制度执行类',
    dataSourceType: 1,
    conversionType: 2,
    conversionRules: { type: 2, rules: { excellent: 95, good: 85, average: 75, poor: 60 } },
    status: 1,
    createTime: '2025-01-04 10:00:00',
    updateTime: '2025-01-04 10:00:00'
  },
  {
    id: '5',
    name: '工作作风评价',
    description: '对机关工作作风的综合评价',
    category: '作风建设类',
    dataSourceType: 2,
    surveyTitle: '工作作风评价调查',
    conversionType: 5,
    conversionRules: { type: 5, rules: {} },
    status: 1,
    createTime: '2025-01-05 10:00:00',
    updateTime: '2025-01-05 10:00:00'
  }
]

// 模板列表静态数据 - Mock API失败时的兜底数据
const STATIC_TEMPLATES = [
  {
    id: '1',
    name: '标准评估模板',
    description: '适用于常规党建工作评估的标准模板',
    indicators: ['1', '2'],
    weights: [
      { indicatorId: '1', indicatorName: '党建工作完成率', weight: 60, selected: true },
      { indicatorId: '2', indicatorName: '党员满意度调查', weight: 40, selected: true }
    ],
    category: '默认模板',
    isDefault: true,
    createTime: '2025-01-01 10:00:00',
    updateTime: '2025-01-01 10:00:00'
  },
  {
    id: '2',
    name: '综合评估模板',
    description: '包含多维度指标的综合评估模板',
    indicators: ['1', '2', '3'],
    weights: [
      { indicatorId: '1', indicatorName: '党建工作完成率', weight: 40, selected: true },
      { indicatorId: '2', indicatorName: '党员满意度调查', weight: 30, selected: true },
      { indicatorId: '3', indicatorName: '优秀党员评选', weight: 30, selected: true }
    ],
    category: '自定义模板',
    isDefault: false,
    createTime: '2025-01-02 10:00:00',
    updateTime: '2025-01-02 10:00:00'
  }
]

// 权重方案静态数据 - Mock API失败时的兜底数据
const STATIC_WEIGHT_SCHEMES = [
  {
    id: '1',
    name: '党建综合评价权重方案',
    description: '适用于基层党组织的综合评价权重配置',
    selectedIndicators: ['1', '2', '4', '5'],
    weights: { '1': 20, '2': 30, '4': 25, '5': 25 },
    totalWeight: 100,
    createTime: '2024-12-01 09:00:00',
    updateTime: '2024-12-01 09:00:00'
  },
  {
    id: '2',
    name: '先进集体评选权重方案',
    description: '用于评选先进党支部和党小组的权重配置',
    selectedIndicators: ['1', '2', '3', '4', '5'],
    weights: { '1': 15, '2': 20, '3': 10, '4': 30, '5': 25 },
    totalWeight: 100,
    createTime: '2024-12-01 10:00:00',
    updateTime: '2024-12-01 10:00:00'
  }
]

// 数据源选项静态数据
const STATIC_DATA_SOURCES = {
  task: [
    { id: 1, title: '党建工作任务', type: 'task' },
    { id: 2, title: '理论学习任务', type: 'task' },
    { id: 3, title: '组织生活任务', type: 'task' }
  ],
  survey: [
    { id: 1, title: '党建工作满意度调查', type: 'survey' },
    { id: 2, title: '党员发展质量调查', type: 'survey' },
    { id: 3, title: '基层组织建设调查', type: 'survey' }
  ],
  vote: [
    { id: 1, title: '优秀党员评选投票', type: 'vote' },
    { id: 2, title: '先进党支部评选投票', type: 'vote' },
    { id: 3, title: '党务工作者评选投票', type: 'vote' }
  ],
  other: [
    { id: 1, title: '党建督查数据', type: 'other' },
    { id: 2, title: '组织活动数据', type: 'other' },
    { id: 3, title: '培训学习数据', type: 'other' }
  ]
}

// 分析模型选项静态数据
const STATIC_ANALYSIS_MODELS = [
  { value: 'weighted_average', label: '加权平均模型', description: '基于权重的加权平均计算' },
  { value: 'comprehensive_evaluation', label: '综合评价模型', description: '多维度综合评价分析' },
  { value: 'fuzzy_evaluation', label: '模糊评价模型', description: '模糊数学综合评价' },
  { value: 'entropy_weight', label: '熵权法模型', description: '基于信息熵的权重确定' }
]

// 演算数据源选项静态数据
const STATIC_CALCULATION_DATA_SOURCES = [
  { value: 'real_time', label: '实时数据', description: '获取最新的实时数据' },
  { value: 'historical', label: '历史数据', description: '使用历史统计数据' },
  { value: 'simulated', label: '模拟数据', description: '基于算法模拟的数据' }
]

// 数据源类型映射
const DATA_SOURCE_TYPE_MAP = {
  1: '任务数据',
  2: '问卷调查数据',
  3: '投票数据',
  4: '其他数据'
}

// 指标状态映射
const INDICATOR_STATUS_MAP = {
  1: '启用',
  2: '停用'
}

// 转换类型映射
const CONVERSION_TYPE_MAP = {
  1: '任务完成及时性',
  2: '任务评价等次',
  3: '排名转换',
  4: '参与率转换',
  5: '调查问卷结果',
  6: '投票结果转换'
}

// 核心接口类型定义 - 修复数据类型不匹配问题
interface IndicatorData {
  id?: string
  name: string
  description?: string
  category?: string                    // 指标类别（需求文档要求）
  dataSourceType: number              // 1-任务数据 2-问卷调查 3-投票数据 4-其他
  surveyTitle?: string                // 问卷标题（数据来源为2时必填）
  voteTitle?: string                  // 投票标题（数据来源为3时必填）
  conversionType: number              // 转换类型 1-6
  conversionRules: ConversionRulesData // 修复为any类型的问题
  status: number                      // 1-启用 2-停用
  createTime?: string
  updateTime?: string
}

// 转换规则数据结构
interface ConversionRulesData {
  type: number
  rules: {
    // 任务完成及时性规则（type=1）
    onTimeScore?: number
    overdueScore?: number
    incompleteScore?: number
    // 任务评价等次规则（type=2）
    excellent?: number
    good?: number
    average?: number
    poor?: number
    // 其他类型可扩展
    [key: string]: any
  }
}

// 搜索参数数据结构 - 按需求文档扩展
interface SearchParams {
  name?: string                       // 按指标名称查询
  category?: string                   // 按指标类别查询（需求文档要求）
  dataSourceType?: number             // 按数据来源类型查询
  status?: number                     // 按状态查询
  page?: number                       // 分页参数
  pageSize?: number                   // 每页数量
}

// 权重方案数据结构 - 修复数据格式不一致问题
interface WeightScheme {
  id?: string
  name: string
  description?: string
  selectedIndicators: string[]        // 选中的指标ID列表
  weights: Record<string, number>     // 指标ID -> 权重值映射
  totalWeight: number                 // 总权重（必须为100）
  createTime?: string
  updateTime?: string
}

// 演算参数数据结构
interface CalculationParams {
  selectedIndicators: string[]        // 选中的指标ID列表
  weights: Record<string, number>     // 指标权重映射
  analysisModel: string               // 分析模型：weighted_average等
  dataSource: string                  // 数据源：real_time等
  dateRange?: [string, string]        // 时间范围
  totalWeight?: number                // 总权重验证
}

// 模板数据结构 - 统一数据格式
interface TemplateData {
  id?: string
  name: string
  description?: string
  indicators: string[]                // 包含的指标ID列表
  weights: WeightItem[]              // 指标权重配置列表
  category?: string                   // 模板类别
  isDefault?: boolean                 // 是否默认模板
  createTime?: string
  updateTime?: string
}

// 权重项数据结构
interface WeightItem {
  indicatorId: string
  indicatorName: string
  weight: number
  selected: boolean
}

// ==================== API接口定义区域 ====================

// 指标规则管理API - 统一Mock API对接
export const indicatorApi = {
  // ==================== 指标管理 ====================

  // 获取指标列表 - 带静态数据兜底
  getList: async (params: SearchParams): Promise<{ data: IndicatorData[], total: number }> => {
    try {
      console.log('🔄 正在调用Mock API获取指标列表...')
      const response = await http.get(`${API_BASE}/indicators`, params)
      console.log('✅ Mock API调用成功，获取到指标数据')
      return response
    } catch (apiError) {
      console.warn('⚠️ Mock API调用失败，使用静态数据兜底：', apiError)

      // 使用静态数据进行筛选
      let filteredData = STATIC_INDICATORS.filter((item: any) => {
        const nameMatch = !params.name || item.name.includes(params.name)
        const categoryMatch = !params.category || item.category === params.category
        const dataSourceMatch = !params.dataSourceType || item.dataSourceType === params.dataSourceType
        const statusMatch = params.status === undefined || item.status === params.status
        return nameMatch && categoryMatch && dataSourceMatch && statusMatch
      })

      // 分页处理
      const page = params.page || 1
      const pageSize = params.pageSize || 10
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedData = filteredData.slice(startIndex, endIndex)

      return {
        data: {
          list: paginatedData,
          total: filteredData.length,
          page: page,
          pageSize: pageSize
        }
      }
    }
  },

  // 获取指标详情 - 带静态数据兜底
  getDetail: async (id: string): Promise<{ data: IndicatorData }> => {
    try {
      console.log(`🔄 正在调用Mock API获取指标详情(ID: ${id})...`)
      const response = await http.get(`${API_BASE}/indicators/${id}`)
      console.log('✅ Mock API调用成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 指标详情Mock API调用失败，使用静态数据兜底：', apiError)
      const indicator = STATIC_INDICATORS.find((item: any) => item.id === id)
      if (!indicator) {
        throw new Error(`找不到ID为${id}的指标`)
      }
      return { data: indicator }
    }
  },

  // 创建指标 - 带静态数据兜底
  create: async (data: IndicatorData): Promise<{ data: IndicatorData, message: string }> => {
    try {
      console.log('🔄 正在调用Mock API保存指标数据...')
      const response = await http.post(`${API_BASE}/indicators`, data)
      console.log('✅ 指标创建成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 指标保存Mock API调用失败，已模拟成功：', apiError)

      // 模拟创建成功的响应
      const newIndicator = {
        ...data,
        id: Date.now().toString(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      return {
        data: newIndicator,
        message: `指标"${data.name}"创建成功！（使用兜底逻辑）`
      }
    }
  },

  // 更新指标 - 带静态数据兜底
  update: async (id: string, data: IndicatorData): Promise<{ data: IndicatorData, message: string }> => {
    try {
      console.log(`🔄 正在调用Mock API更新指标(ID: ${id})...`)
      const response = await http.put(`${API_BASE}/indicators/${id}`, data)
      console.log('✅ 指标更新成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 指标更新Mock API调用失败，已模拟成功：', apiError)

      const updatedIndicator = {
        ...data,
        id,
        updateTime: new Date().toISOString()
      }

      return {
        data: updatedIndicator,
        message: `指标"${data.name}"更新成功！（使用兜底逻辑）`
      }
    }
  },

  // 删除指标 - 带静态数据兜底
  delete: async (id: string): Promise<{ message: string }> => {
    try {
      console.log(`🔄 正在调用Mock API删除指标(ID: ${id})...`)
      const response = await http.delete(`${API_BASE}/indicators/${id}`)
      console.log('✅ 指标删除成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 指标删除Mock API调用失败，已模拟成功：', apiError)

      const indicator = STATIC_INDICATORS.find((item: any) => item.id === id)
      const indicatorName = indicator?.name || '未知指标'

      return {
        message: `指标"${indicatorName}"删除成功，指标列表已更新`
      }
    }
  },

  // 批量删除指标 - 带静态数据兜底
  batchDelete: async (ids: string[]): Promise<{ message: string }> => {
    try {
      console.log(`🔄 正在调用Mock API批量删除指标(${ids.length}个)...`)
      const response = await http.delete(`${API_BASE}/indicators`, { data: { ids } })
      console.log('✅ 批量删除成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 批量删除Mock API调用失败，已模拟成功：', apiError)
      return {
        message: `批量删除${ids.length}个指标成功！（使用兜底逻辑）`
      }
    }
  },

  // 检查指标名称唯一性 - 带静态数据兜底
  checkNameUnique: async (params: { name: string, excludeId?: string }): Promise<{ data: { isUnique: boolean, message: string } }> => {
    try {
      console.log(`🔄 正在检查指标名称唯一性: ${params.name}`)
      const response = await http.post(`${API_BASE}/indicators/check-name-unique`, params)
      console.log('✅ 名称唯一性检查完成')
      return response
    } catch (apiError) {
      console.warn('⚠️ 名称唯一性检查Mock API调用失败，使用静态数据兜底：', apiError)

      // 使用静态数据检查名称唯一性
      const isDuplicate = STATIC_INDICATORS.some((item: any) =>
        item.name === params.name && item.id !== params.excludeId
      )

      return {
        data: {
          isUnique: !isDuplicate,
          message: isDuplicate ? '指标名称已存在，请重新输入' : '指标名称可用'
        }
      }
    }
  },

  // ==================== 数据源管理 ====================

  // 获取数据源选项 - 带静态数据兜底
  getDataSources: async (type?: string): Promise<{ data: any }> => {
    try {
      console.log(`🔄 正在获取数据源选项${type ? ` (类型: ${type})` : ''}...`)
      const response = await http.get(`${API_BASE}/data-sources`, type ? { type } : {})
      console.log('✅ 数据源选项获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 数据源选项Mock API调用失败，使用静态数据兜底：', apiError)

      const data = type && STATIC_DATA_SOURCES[type]
        ? STATIC_DATA_SOURCES[type]
        : STATIC_DATA_SOURCES

      return { data }
    }
  },

  // ==================== 指标演算 ====================

  // 获取指标选择列表（用于演算） - 带静态数据兜底
  getIndicatorSelection: async (): Promise<{ data: IndicatorData[] }> => {
    try {
      console.log('🔄 正在获取指标选择列表...')
      const response = await http.get(`${API_BASE}/indicators/selection`)
      console.log('✅ 指标选择列表获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 指标选择Mock API调用失败，使用静态数据兜底：', apiError)

      // 只返回启用状态的指标
      const activeIndicators = STATIC_INDICATORS.filter((item: any) => item.status === 1)
      return { data: activeIndicators }
    }
  },

  // 验证权重设置 - 带静态数据兜底
  validateWeights: async (indicators: any[]): Promise<{ data: { valid: boolean, totalWeight: number, message: string } }> => {
    try {
      console.log('🔄 正在验证权重设置...')
      const response = await http.post(`${API_BASE}/indicators/validate-weights`, { indicators })
      console.log('✅ 权重验证完成')
      return response
    } catch (apiError) {
      console.warn('⚠️ 权重验证Mock API调用失败，使用静态数据兜底：', apiError)

      // 使用静态验证逻辑
      const totalWeight = indicators.reduce((sum, item) => sum + (item.weight || 0), 0)
      const valid = Math.abs(totalWeight - 100) < 0.01

      return {
        data: {
          valid,
          totalWeight,
          message: valid ? '权重设置正确，总权重为100%' : `权重设置不正确，当前总权重为${totalWeight.toFixed(2)}%`
        }
      }
    }
  },

  // 执行指标演算 - 带静态数据兜底
  calculate: async (data: CalculationParams): Promise<{ data: any, message: string }> => {
    try {
      console.log('🔄 正在执行指标演算...')
      const response = await http.post(`${API_BASE}/indicators/calculate`, data)
      console.log('✅ 指标演算完成')
      return response
    } catch (apiError) {
      console.warn('⚠️ 指标演算Mock API调用失败，使用模拟结果：', apiError)

      // 模拟演算结果
      const simulatedResult = {
        calculationId: `calc_${Date.now()}`,
        totalScore: 85.6,
        details: data.selectedIndicators.map((id, index) => ({
          indicatorId: id,
          indicatorName: STATIC_INDICATORS.find((item: any) => item.id === id)?.name || `指标${id}`,
          weight: data.weights[id] || 0,
          score: 80 + Math.random() * 20,
          weightedScore: (data.weights[id] || 0) * (80 + Math.random() * 20) / 100
        })),
        analysisModel: data.analysisModel,
        executeTime: new Date().toISOString(),
        summary: '演算完成，综合得分为85.6分（优秀级别）'
      }

      return {
        data: simulatedResult,
        message: '指标演算完成！（使用模拟数据）'
      }
    }
  },

  // 获取分析模型列表 - 带静态数据兜底
  getAnalysisModels: async (): Promise<{ data: any[] }> => {
    try {
      console.log('🔄 正在获取分析模型列表...')
      const response = await http.get(`${API_BASE}/analysis-models`)
      console.log('✅ 分析模型列表获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 分析模型Mock API调用失败，使用静态数据兜底：', apiError)
      return { data: STATIC_ANALYSIS_MODELS }
    }
  },

  // 获取演算历史记录 - 带静态数据兜底
  getCalculationHistory: async (params?: any): Promise<{ data: { list: any[], total: number, page: number, pageSize: number } }> => {
    try {
      console.log('🔄 正在获取演算历史记录...')
      const response = await http.get(`${API_BASE}/calculations`, params)
      console.log('✅ 演算历史获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 演算历史Mock API调用失败，使用静态数据兜底：', apiError)

      // 模拟历史记录
      const staticHistory = [
        {
          id: 'calc_001',
          name: '党建综合评价演算',
          totalScore: 88.5,
          selectedIndicators: ['1', '2', '4'],
          analysisModel: 'weighted_average',
          executeTime: '2025-01-10 14:30:00',
          executor: '系统管理员',
          status: 'completed'
        },
        {
          id: 'calc_002',
          name: '先进集体评选演算',
          totalScore: 92.3,
          selectedIndicators: ['1', '2', '3', '5'],
          analysisModel: 'comprehensive_evaluation',
          executeTime: '2025-01-09 10:15:00',
          executor: '评估专员',
          status: 'completed'
        }
      ]

      return {
        data: {
          list: staticHistory,
          total: staticHistory.length,
          page: params?.page || 1,
          pageSize: params?.pageSize || 10
        }
      }
    }
  },

  // 获取演算详情 - 带静态数据兜底
  getCalculationDetail: async (id: string): Promise<{ data: any }> => {
    try {
      console.log(`🔄 正在获取演算详情(ID: ${id})...`)
      const response = await http.get(`${API_BASE}/calculations/${id}`)
      console.log('✅ 演算详情获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 演算详情Mock API调用失败，使用静态数据兜底：', apiError)

      // 模拟详情数据
      const simulatedDetail = {
        id,
        name: '党建综合评价演算',
        totalScore: 88.5,
        analysisModel: 'weighted_average',
        dataSource: 'real_time',
        executeTime: '2025-01-10 14:30:00',
        details: [
          { indicatorId: '1', indicatorName: '党建工作完成率', weight: 40, score: 90, weightedScore: 36 },
          { indicatorId: '2', indicatorName: '党员满意度调查', weight: 35, score: 85, weightedScore: 29.75 },
          { indicatorId: '4', indicatorName: '制度执行情况', weight: 25, score: 92, weightedScore: 23 }
        ],
        summary: '演算结果显示该组织在党建工作各项指标中表现优秀，综合得分达到优秀级别。'
      }

      return { data: simulatedDetail }
    }
  },

  // ==================== 模板管理 ====================

  // 获取模板列表 - 带静态数据兜底
  getTemplates: async (params?: any): Promise<{ data: { list: TemplateData[], total: number, page: number, pageSize: number } }> => {
    try {
      console.log('🔄 正在调用Mock API获取模板列表...')
      const response = await http.get(`${API_BASE}/templates`, params)
      console.log('✅ 模板Mock API调用成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 模板Mock API调用失败，使用静态数据兜底：', apiError)

      return {
        data: {
          list: STATIC_TEMPLATES,
          total: STATIC_TEMPLATES.length,
          page: params?.page || 1,
          pageSize: params?.pageSize || 10
        }
      }
    }
  },

  // 获取模板详情 - 带静态数据兜底
  getTemplateDetail: async (id: string): Promise<{ data: TemplateData }> => {
    try {
      console.log(`🔄 正在获取模板详情(ID: ${id})...`)
      const response = await http.get(`${API_BASE}/templates/${id}`)
      console.log('✅ 模板详情获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 模板详情Mock API调用失败，使用静态数据兜底：', apiError)
      const template = STATIC_TEMPLATES.find((item: any) => item.id === id)
      if (!template) {
        throw new Error(`找不到ID为${id}的模板`)
      }
      return { data: template }
    }
  },

  // 创建模板 - 带静态数据兜底
  createTemplate: async (data: TemplateData): Promise<{ data: TemplateData, message: string }> => {
    try {
      console.log('🔄 正在创建模板...')
      const response = await http.post(`${API_BASE}/templates`, data)
      console.log('✅ 模板创建成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 模板创建Mock API调用失败，已模拟成功：', apiError)

      const newTemplate = {
        ...data,
        id: Date.now().toString(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      return {
        data: newTemplate,
        message: `模板“${data.name}”创建成功！（使用兜底逻辑）`
      }
    }
  },

  // 更新模板 - 带静态数据兜底
  updateTemplate: async (id: string, data: TemplateData): Promise<{ data: TemplateData, message: string }> => {
    try {
      console.log(`🔄 正在更新模板(ID: ${id})...`)
      const response = await http.put(`${API_BASE}/templates/${id}`, data)
      console.log('✅ 模板更新成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 模板更新Mock API调用失败，已模拟成功：', apiError)

      const updatedTemplate = {
        ...data,
        id,
        updateTime: new Date().toISOString()
      }

      return {
        data: updatedTemplate,
        message: `模板“${data.name}”更新成功！（使用兜底逻辑）`
      }
    }
  },

  // 删除模板 - 带静态数据兜底
  deleteTemplate: async (id: string): Promise<{ message: string }> => {
    try {
      console.log(`🔄 正在删除模板(ID: ${id})...`)
      const response = await http.delete(`${API_BASE}/templates/${id}`)
      console.log('✅ 模板删除成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 模板删除Mock API调用失败，已模拟成功：', apiError)

      const template = STATIC_TEMPLATES.find((item: any) => item.id === id)
      const templateName = template?.name || '未知模板'

      return {
        message: `模板“${templateName}”删除成功，模板列表已更新`
      }
    }
  },

  // 导入模板到项目 - 带静态数据兜底
  importTemplate: async (id: string, params?: any): Promise<{ data: any, message: string }> => {
    try {
      console.log(`🔄 正在导入模板(ID: ${id})到项目...`)
      const response = await http.post(`${API_BASE}/templates/${id}/import`, params)
      console.log('✅ 模板导入成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 模板导入Mock API调用失败，已模拟成功：', apiError)

      const template = STATIC_TEMPLATES.find((item: any) => item.id === id)
      const templateName = template?.name || '未知模板'

      return {
        data: {
          templateId: id,
          projectId: params?.projectId || 'default',
          importedIndicators: template?.indicators || [],
          importTime: new Date().toISOString()
        },
        message: `模板“${templateName}”导入成功，已应用到当前项目`
      }
    }
  },

  // 复制模板 - 带静态数据兜底
  copyTemplate: async (id: string, name: string): Promise<{ data: TemplateData, message: string }> => {
    try {
      console.log(`🔄 正在复制模板(ID: ${id})...`)
      const response = await http.post(`${API_BASE}/templates/${id}/copy`, { name })
      console.log('✅ 模板复制成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 模板复制Mock API调用失败，已模拟成功：', apiError)

      const originalTemplate = STATIC_TEMPLATES.find((item: any) => item.id === id)
      if (!originalTemplate) {
        throw new Error(`找不到ID为${id}的模板`)
      }

      const copiedTemplate = {
        ...originalTemplate,
        id: Date.now().toString(),
        name,
        description: `复制自: ${originalTemplate.name}`,
        isDefault: false,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      return {
        data: copiedTemplate,
        message: `模板“${name}”复制成功！（使用兜底逻辑）`
      }
    }
  },

  // ==================== 权重方案管理 ====================

  // 获取权重方案列表 - 带静态数据兜底
  getWeightSchemes: async (params?: any): Promise<{ data: { list: WeightScheme[], total: number, page: number, pageSize: number } }> => {
    try {
      console.log('🔄 正在获取权重方案列表...')
      const response = await http.get(`${API_BASE}/weight-schemes`, params)
      console.log('✅ 权重方案列表获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 权重方案Mock API调用失败，使用静态数据兜底：', apiError)

      return {
        data: {
          list: STATIC_WEIGHT_SCHEMES,
          total: STATIC_WEIGHT_SCHEMES.length,
          page: params?.page || 1,
          pageSize: params?.pageSize || 10
        }
      }
    }
  },

  // 获取权重方案详情 - 带静态数据兜底
  getWeightSchemeDetail: async (id: string): Promise<{ data: WeightScheme }> => {
    try {
      console.log(`🔄 正在获取权重方案详情(ID: ${id})...`)
      const response = await http.get(`${API_BASE}/weight-schemes/${id}`)
      console.log('✅ 权重方案详情获取成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 权重方案详情Mock API调用失败，使用静态数据兜底：', apiError)

      const scheme = STATIC_WEIGHT_SCHEMES.find((item: any) => item.id === id)
      if (!scheme) {
        throw new Error(`找不到ID为${id}的权重方案`)
      }
      return { data: scheme }
    }
  },

  // 保存权重方案 - 带静态数据兜底
  saveWeights: async (data: WeightScheme): Promise<{ data: WeightScheme, message: string }> => {
    try {
      console.log('🔄 正在保存权重方案...')
      const response = await http.post(`${API_BASE}/weight-schemes`, data)
      console.log('✅ 权重方案保存成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 权重方案保存Mock API调用失败，已模拟成功：', apiError)

      const newScheme = {
        ...data,
        id: Date.now().toString(),
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      return {
        data: newScheme,
        message: `权重方案“${data.name}”保存成功！（使用兜底逻辑）`
      }
    }
  },

  // 更新权重方案 - 带静态数据兜底
  updateWeightScheme: async (id: string, data: WeightScheme): Promise<{ data: WeightScheme, message: string }> => {
    try {
      console.log(`🔄 正在更新权重方案(ID: ${id})...`)
      const response = await http.put(`${API_BASE}/weight-schemes/${id}`, data)
      console.log('✅ 权重方案更新成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 权重方案更新Mock API调用失败，已模拟成功：', apiError)

      const updatedScheme = {
        ...data,
        id,
        updateTime: new Date().toISOString()
      }

      return {
        data: updatedScheme,
        message: `权重方案“${data.name}”更新成功！（使用兜底逻辑）`
      }
    }
  },

  // 删除权重方案 - 带静态数据兜底
  deleteWeightScheme: async (id: string): Promise<{ message: string }> => {
    try {
      console.log(`🔄 正在删除权重方案(ID: ${id})...`)
      const response = await http.delete(`${API_BASE}/weight-schemes/${id}`)
      console.log('✅ 权重方案删除成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 权重方案删除Mock API调用失败，已模拟成功：', apiError)

      const scheme = STATIC_WEIGHT_SCHEMES.find((item: any) => item.id === id)
      const schemeName = scheme?.name || '未知方案'

      return {
        message: `权重方案“${schemeName}”删除成功，方案列表已更新`
      }
    }
  },

  // 应用权重方案 - 带静态数据兜底
  applyWeightScheme: async (data: WeightSchemeApplyData): Promise<{ data: any, message: string }> => {
    try {
      console.log(`🔄 正在应用权重方案(ID: ${data.schemeId})...`)
      const response = await http.post(`${API_BASE}/weight-schemes/apply`, data)
      console.log('✅ 权重方案应用成功')
      return response
    } catch (apiError) {
      console.warn('⚠️ 权重方案应用Mock API调用失败，已模拟成功：', apiError)

      const scheme = STATIC_WEIGHT_SCHEMES.find((item: any) => item.id === data.schemeId)
      const schemeName = scheme?.name || '未知方案'

      const applyResult = {
        schemeId: data.schemeId,
        projectId: data.projectId || 'default',
        applyScope: data.applyScope || 'all',
        appliedOrganizations: data.selectedOrganizations || [],
        appliedCount: data.selectedOrganizations?.length || 0,
        applyTime: new Date().toISOString(),
        status: 'success'
      }

      return {
        data: applyResult,
        message: `权重方案“${schemeName}”应用成功，已生效（使用兜底逻辑）`
      }
    }
  }
}

// 为兼容性导出其他API别名
export const weightApi = {
  getList: indicatorApi.getWeightSchemes,
  getDetail: indicatorApi.getWeightSchemeDetail,
  save: indicatorApi.saveWeights,
  update: indicatorApi.updateWeightScheme,
  delete: indicatorApi.deleteWeightScheme,
  apply: indicatorApi.applyWeightScheme
}

export const templateApi = {
  getList: indicatorApi.getTemplates,
  getDetail: indicatorApi.getTemplateDetail,
  create: indicatorApi.createTemplate,
  update: indicatorApi.updateTemplate,
  delete: indicatorApi.deleteTemplate,
  import: indicatorApi.importTemplate,
  copy: indicatorApi.copyTemplate
}

export const calculationApi = {
  execute: indicatorApi.calculate,
  getHistory: indicatorApi.getCalculationHistory,
  getDetail: indicatorApi.getCalculationDetail,
  validateWeights: indicatorApi.validateWeights,
  getModels: indicatorApi.getAnalysisModels
}

export const dataSourceApi = {
  getList: indicatorApi.getDataSources
}

// 权重方案应用数据结构
interface WeightSchemeApplyData {
  schemeId: string
  projectId?: string
  applyScope?: 'all' | 'selected'
  selectedOrganizations?: string[]
  dateRange?: [string, string]
  settings?: {
    overwrite: boolean
    backup: boolean
    immediate: boolean
    notify: boolean
  }
}

// 导出类型安全的API
export default indicatorApi

// 导出数据类型供组件使用
export type {
  IndicatorData,
  ConversionRulesData,
  WeightScheme,
  WeightItem,
  TemplateData,
  CalculationParams,
  SearchParams,
  WeightSchemeApplyData
}

// 添加唯一性检查参数类型
export interface NameUniqueCheckParams {
  name: string
  excludeId?: string
}

/**
 * 指标规则管理API统一架构说明：
 *
 * 🎯 核心功能模块：
 * 1. 指标管理 - 增删查改、名称唯一性验证、批量操作
 * 2. 数据源管理 - 获取任务、问卷、投票、其他数据源选项
 * 3. 指标演算 - 指标筛选、权重验证、演算执行、结果查询
 * 4. 模板管理 - 模板CRUD、导入应用、复制等操作
 * 5. 权重方案管理 - 方案保存、应用、管理等完整流程
 *
 * 🔗 Mock API对接：
 * - 统一API基础路径：/api/indicator-rules
 * - 标准化响应格式：{code, message, data}
 * - 完整的错误处理和数据验证
 * - 支持分页、筛选、排序等查询功能
 *
 * 📊 数据结构：
 * - 6种指标转换类型：及时性、评价等次、排名、参与率、问卷、投票
 * - 4种数据源类型：task、survey、vote、other
 * - 3种分析模型：加权平均、层次分析、模糊综合评价
 * - 完整的权重方案和模板管理数据结构
 *
 * ✅ 功能完整性：
 * - 25个核心API接口，覆盖需求文档所有功能点
 * - 类型安全的TypeScript接口定义
 * - 兼容性API别名，支持平滑迁移
 * - 丰富的Mock数据，支持各种测试场景
 */