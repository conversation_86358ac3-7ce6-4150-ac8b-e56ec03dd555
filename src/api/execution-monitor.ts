// 执行监控模块API接口定义
import request from '@/utils/request'
import { 
  createFallbackDataSource,
  createAPIDataSource,
  type DataSourceInfo
} from '@/data/static-fallback-data'
import type { 
  HealthCheckTask,
  HealthCheckTaskSearchParams
} from '@/types/health-check'

// 带数据源信息的返回类型
export interface ApiResponseWithSource<T> {
  data: T
  dataSource: DataSourceInfo
}

// ================================
// 静态降级数据
// ================================

// 静态任务数据
const FALLBACK_TASKS: HealthCheckTask[] = [
  {
    id: 1,
    taskName: '党组织设置规范性检查',
    taskType: 1,
    scheduleType: 1,
    status: 3,
    progress: 100,
    startTime: '2025-09-11 10:30:00',
    endTime: '2025-09-11 10:35:00',
    createTime: '2025-09-11 10:29:00',
    operator: '张三',
    description: '检查党组织设置的规范性'
  },
  {
    id: 2,
    taskName: '党务干部任免程序检查',
    taskType: 2,
    scheduleType: 1,
    status: 3,
    progress: 100,
    startTime: '2025-09-11 09:45:00',
    endTime: '2025-09-11 09:50:00',
    createTime: '2025-09-11 09:44:00',
    operator: '李四',
    description: '检查党务干部任免程序的合规性'
  },
  {
    id: 3,
    taskName: '重点任务完成情况体检',
    taskType: 3,
    scheduleType: 1,
    status: 2,
    progress: 65,
    startTime: '2025-09-11 11:00:00',
    endTime: '',
    createTime: '2025-09-11 10:59:00',
    operator: '王五',
    description: '检查重点任务的完成情况'
  },
  {
    id: 4,
    taskName: '用户信息完整性验证',
    taskType: 4,
    scheduleType: 1,
    status: 1,
    progress: 0,
    startTime: '',
    endTime: '',
    createTime: '2025-09-11 16:20:00',
    operator: '赵六',
    description: '检查用户信息的完整性'
  }
]

// 静态执行结果数据
const FALLBACK_EXECUTION_RESULT = {
  taskId: Date.now(),
  executeTime: new Date().toISOString(),
  checkTypes: [1, 2, 3, 4],
  totalChecked: 256,
  exceptions: [
    {
      id: 1,
      type: '数据缺失',
      table: '党组织信息表',
      field: '成立时间',
      level: 2,
      description: '部分党组织成立时间字段为空'
    },
    {
      id: 2,
      type: '数据不一致',
      table: '党务干部表',
      field: '任职状态',
      level: 3,
      description: '存在任职状态与实际情况不符的记录'
    }
  ],
  summary: '体检任务执行完成，检查了256条数据，发现2个异常项'
}

// ================================
// API接口函数
// ================================

/**
 * 获取体检任务列表
 */
export const fetchHealthCheckTaskList = async (params?: HealthCheckTaskSearchParams): Promise<ApiResponseWithSource<{
  data: HealthCheckTask[]
  total: number
  page: number
  pageSize: number
}>> => {
  try {
    const response = await request.get('/api/data-inspection/health-check/tasks', {
      params
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取任务列表失败，使用静态数据:', error)
    
    // 模拟分页处理
    const page = params?.page || 1
    const pageSize = params?.pageSize || 10
    let filteredTasks = [...FALLBACK_TASKS]
    
    // 根据参数筛选
    if (params?.status) {
      filteredTasks = filteredTasks.filter(task => task.status === params.status)
    }
    if (params?.taskType) {
      filteredTasks = filteredTasks.filter(task => task.taskType === params.taskType)
    }
    
    // 分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedTasks = filteredTasks.slice(startIndex, endIndex)
    
    return {
      data: {
        data: paginatedTasks,
        total: filteredTasks.length,
        page,
        pageSize
      },
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 执行体检任务
 */
export const executeHealthCheckTask = async (taskId?: number, checkTypes?: number[]): Promise<ApiResponseWithSource<{
  taskId: number
  executeTime: string
  checkTypes: number[]
  totalChecked: number
  exceptions: Array<{
    id: number
    type: string
    table: string
    field: string
    level: number
    description: string
  }>
  summary: string
}>> => {
  try {
    const response = await request.post('/api/data-inspection/health-check/execute', {
      taskId,
      checkTypes: checkTypes || [1, 2, 3, 4]
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('执行体检任务失败，使用静态数据:', error)
    
    // 模拟执行结果
    const mockResult = {
      ...FALLBACK_EXECUTION_RESULT,
      taskId: taskId || Date.now(),
      executeTime: new Date().toISOString(),
      checkTypes: checkTypes || [1, 2, 3, 4]
    }
    
    return {
      data: mockResult,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 批量执行体检任务
 */
export const batchExecuteHealthCheck = async (checkTypes: number[], config?: any): Promise<ApiResponseWithSource<{
  batchId: number
  executeTime: string
  checkTypes: number[]
  totalTasks: number
  completedTasks: number
  exceptions: any[]
  summary: string
}>> => {
  try {
    const response = await request.post('/api/data-inspection/health-check/batch-execute', {
      checkTypes,
      config
    })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('批量执行体检失败，使用静态数据:', error)
    
    const mockResult = {
      batchId: Date.now(),
      executeTime: new Date().toISOString(),
      checkTypes: checkTypes || [1, 2, 3, 4],
      totalTasks: checkTypes?.length || 4,
      completedTasks: checkTypes?.length || 4,
      exceptions: FALLBACK_EXECUTION_RESULT.exceptions,
      summary: `批量执行完成，共处理${checkTypes?.length || 4}个任务，发现${FALLBACK_EXECUTION_RESULT.exceptions.length}个异常项`
    }
    
    return {
      data: mockResult,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 停止执行任务
 */
export const stopHealthCheckExecution = async (taskId: number): Promise<ApiResponseWithSource<boolean>> => {
  try {
    const response = await request.post('/api/data-inspection/health-check/stop', { taskId })
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('停止执行任务失败，使用静态响应:', error)
    return {
      data: true,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

/**
 * 获取任务执行状态
 */
export const getTaskExecutionStatus = async (taskId: number): Promise<ApiResponseWithSource<{
  taskId: number
  status: number
  progress: number
  startTime?: string
  endTime?: string
  currentStep?: string
}>> => {
  try {
    const response = await request.get(`/api/data-inspection/health-check/tasks/${taskId}/status`)
    return {
      data: response.data,
      dataSource: createAPIDataSource()
    }
  } catch (error) {
    console.warn('获取任务状态失败，使用静态数据:', error)
    
    // 从静态任务列表中查找对应任务
    const task = FALLBACK_TASKS.find(t => t.id === taskId)
    const mockStatus = {
      taskId,
      status: task?.status || 1,
      progress: task?.progress || 0,
      startTime: task?.startTime,
      endTime: task?.endTime,
      currentStep: task?.status === 2 ? '正在执行中...' : undefined
    }
    
    return {
      data: mockStatus,
      dataSource: createFallbackDataSource(error instanceof Error ? error.message : '网络请求失败')
    }
  }
}

// ================================
// 兼容性导出
// ================================

export const executionMonitorApi = {
  fetchTaskList: fetchHealthCheckTaskList,
  executeTask: executeHealthCheckTask,
  batchExecute: batchExecuteHealthCheck,
  stopExecution: stopHealthCheckExecution,
  getTaskStatus: getTaskExecutionStatus
}

export default executionMonitorApi